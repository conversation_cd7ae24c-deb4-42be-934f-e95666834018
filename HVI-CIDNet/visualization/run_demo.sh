#!/bin/bash

# HVI色彩空间可视化工具快速运行脚本
# 作者: Claude Code
# 日期: 2025-08-06

echo "=== HVI色彩空间可视化工具 ==="
echo

# 检查是否在正确的目录
if [ ! -f "net/HVI_transform.py" ]; then
    echo "错误: 请在HVI-CIDNet项目根目录下运行此脚本"
    echo "当前目录: $(pwd)"
    exit 1
fi

# 检查Python环境
if ! command -v python &> /dev/null; then
    echo "错误: 未找到Python命令"
    exit 1
fi

# 进入visualization目录
cd visualization

# 检查脚本是否存在
if [ ! -f "visualize_hvi.py" ]; then
    echo "错误: 未找到可视化脚本"
    exit 1
fi

echo "选择要运行的可视化工具:"
echo "1) 基础HVI可视化工具 (推荐)"
echo "2) HVI通道分离演示"
echo "3) RGB-HVI对比工具"
echo "4) 运行所有工具"
echo "5) 退出"
echo

read -p "请输入选择 (1-5): " choice

case $choice in
    1)
        echo "启动基础HVI可视化工具..."
        python visualize_hvi.py
        ;;
    2)
        echo "启动HVI通道分离演示..."
        python hvi_channels_demo.py
        ;;
    3)
        echo "启动RGB-HVI对比工具..."
        python rgb_hvi_comparison.py
        ;;
    4)
        echo "运行所有可视化工具..."
        echo
        echo "=== 1) 基础HVI可视化工具 ==="
        python visualize_hvi.py
        echo
        echo "=== 2) HVI通道分离演示 ==="
        python hvi_channels_demo.py
        echo
        echo "=== 3) RGB-HVI对比工具 ==="
        python rgb_hvi_comparison.py
        ;;
    5)
        echo "退出"
        exit 0
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo
echo "可视化完成！"
echo "如需查看详细使用说明，请查看 visualization/README.md"
#!/usr/bin/env python3
"""
快速测试脚本 - 演示HVI色彩空间可视化工具
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt
import torchvision.transforms as transforms
from net.HVI_transform import RGB_HVI

def quick_demo():
    """快速演示HVI色彩空间转换"""
    
    print("=== HVI色彩空间快速演示 ===")
    
    # 创建一个简单的测试图像
    test_colors = [
        ([1.0, 0.0, 0.0], '红色'),
        ([0.0, 1.0, 0.0], '绿色'),
        ([0.0, 0.0, 1.0], '蓝色'),
        ([1.0, 1.0, 0.0], '黄色'),
    ]
    
    transform = RGB_HVI()
    
    print("测试颜色转换:")
    print("-" * 50)
    
    for color, name in test_colors:
        img_tensor = torch.tensor(color).float().view(1, 3, 1, 1)
        hvi_tensor = transform.HVIT(img_tensor)
        
        H = hvi_tensor[0, 0, 0, 0].item()
        V = hvi_tensor[0, 1, 0, 0].item()
        I = hvi_tensor[0, 2, 0, 0].item()
        
        print(f"{name:8s}: RGB={color} -> H={H:6.3f}, V={V:6.3f}, I={I:6.3f}")
    
    print("-" * 50)
    print("HVI通道说明:")
    print("H通道: 色调的余弦分量，范围[-1, 1]")
    print("V通道: 色调的正弦分量，范围[-1, 1]")
    print("I通道: 强度/亮度，范围[0, 1]")
    print()
    print("使用方法:")
    print("1. 运行: python visualize_hvi.py")
    print("2. 选择分析类型")
    print("3. 输入图像路径")
    print("4. 查看可视化结果")

if __name__ == "__main__":
    quick_demo()
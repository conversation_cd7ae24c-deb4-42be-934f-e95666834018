# LLIE 项目文档

本目录包含低光图像增强（LLIE）项目的完整文档，按功能模块组织便于学习和查找。

## 📚 文档结构

### 📖 使用指南 (`guides/`)
新用户推荐的学习路径：
1. **[环境设置](guides/environment_setup.md)** - 开发环境配置
2. **[快速开始](guides/quick_start_guide.md)** - 快速上手指南
3. **[训练教程](guides/training_tutorial.md)** - 完整的训练指南
4. **[推理指南](guides/inference_guide.md)** - 如何对新图像进行推理
5. **[评估方法](guides/evaluation_methodology.md)** - 评估工作原理
6. **[数据处理](guides/data_processing_guide.md)** - 数据加载和预处理
7. **[配置指南](guides/configuration_guide.md)** - 理解和自定义配置

### 🔧 技术文档 (`technical/`)
深入的技术实现细节：
- **[技术实现流程](technical/technical_implementation_flow.md)** - 详细技术概述
- **[架构修改](technical/architecture_modification.md)** - 如何修改模型架构
- **[任务完成总结](technical/task_completion_summary.md)** - 最近任务完成情况
- **[重构总结](technical/REFACTOR_SUMMARY.md)** - 重构工作总结
- **[重构需求](technical/重构需求.md)** - 重构需求文档
- **[配置插值修复](technical/configuration_interpolation_fix.md)** - 配置系统修复
- **[从零开始项目](technical/project_from_scratch.md)** - 项目构建指南

### ⚡ 优化文档 (`optimization/`)
项目优化和现代化相关：
- **[LLIE优化总结](optimization/LLIE_optimization_summary.md)** - 完整优化报告
- **[Conda到UV迁移指南](optimization/conda_to_uv_migration_guide.md)** - 包管理器迁移
- **[UV环境指南](optimization/uv_environment_guide.md)** - UV使用指南
- **[pyproject.toml分析](optimization/pyproject_analysis.md)** - 项目配置分析
- **[pyproject.toml优化报告](optimization/pyproject_optimization_report.md)** - 优化结果

## 🚀 快速开始

### 新用户推荐路径
```bash
# 1. 环境设置
docs/guides/environment_setup.md

# 2. 快速开始
docs/guides/quick_start_guide.md

# 3. 训练第一个模型
docs/guides/training_tutorial.md
```

### 开发者路径
```bash
# 1. 技术实现流程
docs/technical/technical_implementation_flow.md

# 2. 架构修改指南
docs/technical/architecture_modification.md

# 3. 优化和现代化
docs/optimization/LLIE_optimization_summary.md
```

## 🛠️ 项目使用

### 基本命令
```bash
# 训练模型
python main.py task=train

# 评估模型
python main.py task=evaluate evaluation.checkpoint_path=/path/to/model.pth

# 推理
python main.py task=inference inference.checkpoint_path=/path/to/model.pth inference.input_path=/path/to/images
```

### UV环境管理
```bash
# 创建环境
uv venv .venv --python 3.9
source .venv/bin/activate

# 安装依赖
uv pip install -e ".[full]"
```
- **评估指标**：PSNR、SSIM、MAE

#### 项目结构
```
LLIE/
├── src/llie/              # 源代码
│   ├── models/           # 模型定义
│   ├── data/             # 数据加载
│   ├── engine/           # 训练引擎
│   ├── tasks/            # 任务定义
│   └── utils/            # 工具函数
├── configs/              # 配置文件
├── data/                 # 数据目录
├── outputs/              # 输出目录
├── notebooks/            # Jupyter notebooks
└── docs/                 # 文档目录
```

### 🔧 常见问题

#### Q: 如何调整模型大小？
A: 修改`model.architecture`中的参数：
```bash
python run.py model.architecture.s_nf=64 model.architecture.num_blocks=8
```

#### Q: 如何使用自己的数据集？
A: 修改`configs/dataset/LOLv2_Real.yaml`中的路径配置，或创建新的数据集配置文件。

#### Q: 如何进行模型评估？
A: 使用evaluate任务：
```bash
python run.py task=evaluate evaluation.checkpoint_path=path/to/model.pth
```

#### Q: 训练过程中出现显存不足怎么办？
A: 尝试以下解决方案：
- 减小batch_size：`trainer.batch_size=4`
- 启用混合精度：`trainer.use_amp=true`
- 减小模型尺寸：`model.architecture.s_nf=16`

### 📞 获取帮助

如果您在使用过程中遇到问题：
1. 首先查看[配置指南](configuration_guide.md)
2. 检查项目的GitHub Issues
3. 查看代码中的详细注释

### 🎯 最佳实践

1. **实验管理**：使用有意义的实验名称
2. **配置管理**：为不同实验创建专门的配置文件
3. **结果记录**：启用W&B进行实验跟踪
4. **代码版本**：使用git管理代码版本
5. **环境隔离**：始终在conda环境中运行

### 📈 性能优化建议

1. **数据加载优化**：
   - 增加`num_workers`
   - 启用`cache_images`（如果内存充足）

2. **训练加速**：
   - 使用混合精度训练
   - 适当增大batch_size
   - 使用更快的数据增强策略

3. **模型优化**：
   - 根据数据集大小调整模型复杂度
   - 使用知识蒸馏技术
   - 考虑模型剪枝和量化

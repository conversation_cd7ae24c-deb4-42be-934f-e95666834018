# 网络架构修改实战指南

本指南深入解析LLIE项目的注册表系统，并提供组件修改、扩展和自定义的实战教程。

## 📋 目录

1. [注册表系统深入解析](#注册表系统深入解析)
2. [LLIE架构解析](#dmfourllie架构解析)
3. [组件修改实战](#组件修改实战)
4. [自定义组件开发](#自定义组件开发)
5. [架构实验设计](#架构实验设计)
6. [性能优化技巧](#性能优化技巧)
7. [调试和验证](#调试和验证)
8. [最佳实践](#最佳实践)

## 🔧 注册表系统深入解析

### 注册表核心机制

LLIE项目使用注册表模式实现组件的即插即用，让您可以轻松替换和扩展网络组件。

```python
# src/llie/utils/registry.py 核心实现
class Registry:
    """注册表类 - 管理组件的注册和构建"""
    
    def __init__(self, name):
        self.name = name
        self.module_dict = {}  # 存储注册的组件
    
    def register(self, name=None):
        """注册装饰器"""
        def decorator(cls):
            # 确定注册名称
            register_name = name if name is not None else cls.__name__
            
            # 检查重复注册
            if register_name in self.module_dict:
                raise KeyError(f'{register_name} 已经在 {self.name} 中注册')
            
            # 注册组件
            self.module_dict[register_name] = cls
            
            # 为类添加注册信息
            cls._registry_name = register_name
            cls._registry = self
            
            return cls
        
        return decorator
    
    def build(self, cfg, *args, **kwargs):
        """构建组件实例"""
        if isinstance(cfg, str):
            # 如果cfg是字符串，直接作为组件名
            component_name = cfg
            component_args = kwargs
        else:
            # 如果cfg是字典，提取组件名和参数
            cfg_dict = cfg if isinstance(cfg, dict) else OmegaConf.to_container(cfg)
            component_name = cfg_dict.pop('type')
            component_args = {**cfg_dict, **kwargs}
        
        # 获取组件类
        if component_name not in self.module_dict:
            raise KeyError(f'{component_name} 未在 {self.name} 中注册')
        
        component_cls = self.module_dict[component_name]
        
        # 创建实例
        return component_cls(*args, **component_args)
    
    def list_modules(self):
        """列出所有注册的组件"""
        return list(self.module_dict.keys())

# 全局注册表实例
MODELS = Registry('models')          # 模型注册表
LOSSES = Registry('losses')          # 损失函数注册表
DATASETS = Registry('datasets')      # 数据集注册表
OPTIMIZERS = Registry('optimizers')  # 优化器注册表
SCHEDULERS = Registry('schedulers')  # 调度器注册表
COMPONENTS = Registry('components')  # 组件注册表

# 便捷的注册装饰器
register_model = MODELS.register
register_loss = LOSSES.register
register_dataset = DATASETS.register
register_optimizer = OPTIMIZERS.register
register_scheduler = SCHEDULERS.register
register_component = COMPONENTS.register
```

### 注册表使用示例

```python
# 1. 注册新模型
@register_model("MyCustomModel")
class MyCustomModel(nn.Module):
    def __init__(self, input_channels=3, output_channels=3):
        super().__init__()
        self.conv = nn.Conv2d(input_channels, output_channels, 3, 1, 1)
    
    def forward(self, x):
        return self.conv(x)

# 2. 注册新损失函数
@register_loss("MyCustomLoss")
class MyCustomLoss(nn.Module):
    def __init__(self, alpha=1.0, beta=0.1):
        super().__init__()
        self.alpha = alpha
        self.beta = beta
        self.l1_loss = nn.L1Loss()
        self.mse_loss = nn.MSELoss()
    
    def forward(self, pred, target):
        l1 = self.l1_loss(pred, target)
        mse = self.mse_loss(pred, target)
        return self.alpha * l1 + self.beta * mse

# 3. 注册新组件
@register_component("MyAttentionBlock")
class MyAttentionBlock(nn.Module):
    def __init__(self, channels, reduction=16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(channels, channels // reduction),
            nn.ReLU(inplace=True),
            nn.Linear(channels // reduction, channels),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y.expand_as(x)

# 4. 查看已注册的组件
print("已注册的模型:", MODELS.list_modules())
print("已注册的损失函数:", LOSSES.list_modules())
print("已注册的组件:", COMPONENTS.list_modules())

# 5. 从配置构建组件
model_config = {
    'type': 'MyCustomModel',
    'input_channels': 3,
    'output_channels': 3
}
model = MODELS.build(model_config)

loss_config = {
    'type': 'MyCustomLoss',
    'alpha': 1.0,
    'beta': 0.2
}
loss_fn = LOSSES.build(loss_config)
```

## 🏗️ LLIE架构解析

### 整体架构概览

```python
# LLIE的四级处理架构
@register_model("LLIE")
class LLIE(BaseArchitecture):
    """
    双模态四级低光图像增强网络
    
    架构组成:
    1. LuminanceMapProcessor - 亮度图处理器
    2. FirstProcessModel - 第一级处理（傅里叶域）
    3. SecondProcessModel - 第二级处理（空间域）
    4. 多级特征融合机制
    """
    
    def __init__(self, y_nf=16, f_nf=16, s_nf=32, num_blocks=6, **kwargs):
        super().__init__()
        
        # 第一级：亮度图处理
        self.luminance_processor = LuminanceMapProcessor(
            in_channels=3, 
            out_channels=y_nf
        )
        
        # 第二级：傅里叶域处理
        self.first_process = FirstProcessModel(
            in_channels=3, 
            nf=f_nf, 
            num_blocks=num_blocks
        )
        
        # 第三级：空间域处理
        self.second_process = SecondProcessModel(
            in_channels=3 + f_nf, 
            nf=s_nf, 
            num_blocks=num_blocks
        )
        
        # 第四级：特征融合和输出
        self.fusion_conv = nn.Conv2d(
            y_nf + s_nf, 3, 
            kernel_size=3, padding=1
        )
        
        # 激活函数
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入低光图像 [B, 3, H, W]
        
        Returns:
            enhanced: 增强后的图像 [B, 3, H, W]
        """
        # 第一级：亮度图处理
        luminance_features = self.luminance_processor(x)
        
        # 第二级：傅里叶域处理
        fourier_features = self.first_process(x)
        
        # 第三级：空间域处理（融合傅里叶特征）
        spatial_input = torch.cat([x, fourier_features], dim=1)
        spatial_features = self.second_process(spatial_input)
        
        # 第四级：特征融合
        fused_features = torch.cat([luminance_features, spatial_features], dim=1)
        enhanced = self.fusion_conv(fused_features)
        enhanced = self.sigmoid(enhanced)
        
        return enhanced
```

### 核心组件详解

#### 1. 亮度图处理器

```python
# src/llie/models/components/luminance_map.py
@register_component("LuminanceMapProcessor")
class LuminanceMapProcessor(nn.Module):
    """
    亮度图处理器 - 专门处理图像的亮度信息
    
    功能:
    - 提取亮度特征
    - 亮度自适应增强
    - 保持颜色一致性
    """
    
    def __init__(self, in_channels=3, out_channels=16, use_attention=True):
        super().__init__()
        
        # 亮度提取层
        self.luminance_conv = nn.Sequential(
            nn.Conv2d(in_channels, out_channels, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, 3, 1, 1),
            nn.ReLU(inplace=True)
        )
        
        # 可选的注意力机制
        if use_attention:
            self.attention = ChannelAttention(out_channels)
        else:
            self.attention = nn.Identity()
        
        # 亮度增强层
        self.enhancement_conv = nn.Sequential(
            nn.Conv2d(out_channels, out_channels, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, 1)
        )
    
    def forward(self, x):
        """处理亮度信息"""
        # 提取亮度特征
        luminance_features = self.luminance_conv(x)
        
        # 应用注意力
        attended_features = self.attention(luminance_features)
        
        # 亮度增强
        enhanced_features = self.enhancement_conv(attended_features)
        
        # 残差连接
        output = luminance_features + enhanced_features
        
        return output

@register_component("ChannelAttention")
class ChannelAttention(nn.Module):
    """通道注意力机制"""
    
    def __init__(self, channels, reduction=16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        
        self.fc = nn.Sequential(
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, channels, 1, bias=False)
        )
        
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        attention = self.sigmoid(avg_out + max_out)
        return x * attention
```

#### 2. 傅里叶域处理模块

```python
# src/llie/models/components/fourier_blocks.py
@register_component("FirstProcessModel")
class FirstProcessModel(nn.Module):
    """
    第一级处理模型 - 傅里叶域处理
    
    功能:
    - 全局频域信息处理
    - 低频和高频分离增强
    - 频域噪声抑制
    """
    
    def __init__(self, in_channels=3, nf=16, num_blocks=6):
        super().__init__()
        
        # 输入卷积
        self.input_conv = nn.Conv2d(in_channels, nf, 3, 1, 1)
        
        # FFC块序列
        self.ffc_blocks = nn.ModuleList([
            FFCBlock(nf, nf) for _ in range(num_blocks)
        ])
        
        # 输出卷积
        self.output_conv = nn.Conv2d(nf, nf, 3, 1, 1)
    
    def forward(self, x):
        """傅里叶域处理"""
        # 输入特征提取
        features = self.input_conv(x)
        
        # 通过FFC块序列
        for ffc_block in self.ffc_blocks:
            features = ffc_block(features)
        
        # 输出特征
        output = self.output_conv(features)
        
        return output

@register_component("FFCBlock")
class FFCBlock(nn.Module):
    """
    快速傅里叶卷积块 (Fast Fourier Convolution Block)
    
    结合空间域和频域处理，实现全局感受野
    """
    
    def __init__(self, channels, ratio_gin=0.5, ratio_gout=0.5):
        super().__init__()
        
        # 计算局部和全局通道数
        self.ratio_gin = ratio_gin
        self.ratio_gout = ratio_gout
        
        in_cg = int(channels * ratio_gin)
        in_cl = channels - in_cg
        out_cg = int(channels * ratio_gout)
        out_cl = channels - out_cg
        
        # 局部分支（空间卷积）
        self.local_conv = nn.Conv2d(in_cl, out_cl, 3, 1, 1) if in_cl > 0 and out_cl > 0 else None
        
        # 全局分支（傅里叶变换）
        self.global_conv = SpectralTransform(in_cg, out_cg) if in_cg > 0 and out_cg > 0 else None
        
        # 交互分支
        self.local_to_global = nn.Conv2d(in_cl, out_cg, 1) if in_cl > 0 and out_cg > 0 else None
        self.global_to_local = nn.Conv2d(in_cg, out_cl, 1) if in_cg > 0 and out_cl > 0 else None
    
    def forward(self, x):
        """FFC块前向传播"""
        # 分离局部和全局特征
        if self.ratio_gin == 0:
            x_l, x_g = x, None
        elif self.ratio_gin == 1:
            x_l, x_g = None, x
        else:
            x_l, x_g = torch.split(x, [int(x.size(1) * (1 - self.ratio_gin)), 
                                      int(x.size(1) * self.ratio_gin)], dim=1)
        
        # 局部处理
        out_l = self.local_conv(x_l) if x_l is not None and self.local_conv is not None else None
        
        # 全局处理
        out_g = self.global_conv(x_g) if x_g is not None and self.global_conv is not None else None
        
        # 交互处理
        if x_l is not None and self.local_to_global is not None:
            l2g = self.local_to_global(x_l)
            out_g = out_g + l2g if out_g is not None else l2g
        
        if x_g is not None and self.global_to_local is not None:
            g2l = self.global_to_local(x_g)
            out_l = out_l + g2l if out_l is not None else g2l
        
        # 合并输出
        if out_l is None:
            return out_g
        elif out_g is None:
            return out_l
        else:
            return torch.cat([out_l, out_g], dim=1)

@register_component("SpectralTransform")
class SpectralTransform(nn.Module):
    """频谱变换层 - 在频域进行卷积操作"""
    
    def __init__(self, in_channels, out_channels, stride=1, groups=1):
        super().__init__()
        
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.stride = stride
        self.groups = groups
        
        # 频域卷积权重
        self.weight = nn.Parameter(
            torch.randn(out_channels, in_channels // groups, 1, 1)
        )
        
        # 可选的偏置
        self.bias = nn.Parameter(torch.zeros(out_channels))
    
    def forward(self, x):
        """频域变换"""
        batch, channels, height, width = x.shape
        
        # FFT变换到频域
        x_fft = torch.fft.rfft2(x, dim=(-2, -1), norm='ortho')
        
        # 在频域进行卷积（点乘）
        weight_fft = torch.fft.rfft2(self.weight, s=(height, width), dim=(-2, -1), norm='ortho')
        
        # 频域卷积
        out_fft = torch.zeros(batch, self.out_channels, height, width // 2 + 1, 
                             dtype=torch.complex64, device=x.device)
        
        for g in range(self.groups):
            start_in = g * (self.in_channels // self.groups)
            end_in = (g + 1) * (self.in_channels // self.groups)
            start_out = g * (self.out_channels // self.groups)
            end_out = (g + 1) * (self.out_channels // self.groups)
            
            out_fft[:, start_out:end_out] = torch.sum(
                x_fft[:, start_in:end_in, None] * weight_fft[start_out:end_out, :],
                dim=2
            )
        
        # IFFT变换回空间域
        out = torch.fft.irfft2(out_fft, s=(height, width), dim=(-2, -1), norm='ortho')
        
        # 添加偏置
        out = out + self.bias.view(1, -1, 1, 1)
        
        return out
```

#### 3. 空间域处理模块

```python
# src/llie/models/components/spatial_blocks.py
@register_component("SecondProcessModel")
class SecondProcessModel(nn.Module):
    """
    第二级处理模型 - 空间域处理
    
    功能:
    - 局部细节增强
    - 边缘保持
    - 纹理恢复
    """
    
    def __init__(self, in_channels, nf=32, num_blocks=6):
        super().__init__()
        
        # 输入卷积
        self.input_conv = nn.Conv2d(in_channels, nf, 3, 1, 1)
        
        # 残差块序列
        self.res_blocks = nn.ModuleList([
            ResidualBlock(nf) for _ in range(num_blocks)
        ])
        
        # 输出卷积
        self.output_conv = nn.Conv2d(nf, nf, 3, 1, 1)
    
    def forward(self, x):
        """空间域处理"""
        # 输入特征提取
        features = self.input_conv(x)
        
        # 通过残差块序列
        for res_block in self.res_blocks:
            features = res_block(features)
        
        # 输出特征
        output = self.output_conv(features)
        
        return output

@register_component("ResidualBlock")
class ResidualBlock(nn.Module):
    """残差块 - 用于深层特征提取"""
    
    def __init__(self, channels, use_attention=True):
        super().__init__()
        
        self.conv1 = nn.Conv2d(channels, channels, 3, 1, 1)
        self.conv2 = nn.Conv2d(channels, channels, 3, 1, 1)
        self.relu = nn.ReLU(inplace=True)
        
        # 可选的注意力机制
        if use_attention:
            self.attention = SpatialAttention()
        else:
            self.attention = nn.Identity()
    
    def forward(self, x):
        """残差块前向传播"""
        residual = x
        
        out = self.conv1(x)
        out = self.relu(out)
        out = self.conv2(out)
        
        # 应用注意力
        out = self.attention(out)
        
        # 残差连接
        out = out + residual
        out = self.relu(out)
        
        return out

@register_component("SpatialAttention")
class SpatialAttention(nn.Module):
    """空间注意力机制"""
    
    def __init__(self, kernel_size=7):
        super().__init__()
        
        self.conv = nn.Conv2d(2, 1, kernel_size, padding=kernel_size//2, bias=False)
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        """空间注意力前向传播"""
        # 计算空间统计
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        
        # 合并统计信息
        attention_input = torch.cat([avg_out, max_out], dim=1)
        
        # 生成注意力图
        attention = self.sigmoid(self.conv(attention_input))
        
        return x * attention
```

## 🔧 组件修改实战

### 实战1：添加新的注意力机制

```python
# 1. 创建新的注意力组件
@register_component("CoordinateAttention")
class CoordinateAttention(nn.Module):
    """坐标注意力机制 - 同时考虑通道和空间信息"""
    
    def __init__(self, channels, reduction=32):
        super().__init__()
        
        self.pool_h = nn.AdaptiveAvgPool2d((None, 1))
        self.pool_w = nn.AdaptiveAvgPool2d((1, None))
        
        mip = max(8, channels // reduction)
        
        self.conv1 = nn.Conv2d(channels, mip, kernel_size=1, stride=1, padding=0)
        self.bn1 = nn.BatchNorm2d(mip)
        self.act = nn.ReLU(inplace=True)
        
        self.conv_h = nn.Conv2d(mip, channels, kernel_size=1, stride=1, padding=0)
        self.conv_w = nn.Conv2d(mip, channels, kernel_size=1, stride=1, padding=0)
        
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        identity = x
        
        n, c, h, w = x.size()
        
        # 水平和垂直池化
        x_h = self.pool_h(x)
        x_w = self.pool_w(x).permute(0, 1, 3, 2)
        
        # 合并特征
        y = torch.cat([x_h, x_w], dim=2)
        y = self.conv1(y)
        y = self.bn1(y)
        y = self.act(y)
        
        # 分离并生成注意力
        x_h, x_w = torch.split(y, [h, w], dim=2)
        x_w = x_w.permute(0, 1, 3, 2)
        
        a_h = self.sigmoid(self.conv_h(x_h))
        a_w = self.sigmoid(self.conv_w(x_w))
        
        # 应用注意力
        out = identity * a_w * a_h
        
        return out

# 2. 修改现有组件使用新注意力
@register_component("EnhancedResidualBlock")
class EnhancedResidualBlock(nn.Module):
    """增强的残差块 - 使用坐标注意力"""
    
    def __init__(self, channels):
        super().__init__()
        
        self.conv1 = nn.Conv2d(channels, channels, 3, 1, 1)
        self.conv2 = nn.Conv2d(channels, channels, 3, 1, 1)
        self.relu = nn.ReLU(inplace=True)
        
        # 使用新的坐标注意力
        self.attention = CoordinateAttention(channels)
    
    def forward(self, x):
        residual = x
        
        out = self.conv1(x)
        out = self.relu(out)
        out = self.conv2(out)
        
        # 应用坐标注意力
        out = self.attention(out)
        
        out = out + residual
        out = self.relu(out)
        
        return out

# 3. 创建使用新组件的模型配置
enhanced_model_config = {
    'type': 'LLIE',
    'y_nf': 16,
    'f_nf': 16,
    's_nf': 32,
    'num_blocks': 6,
    'residual_block_type': 'EnhancedResidualBlock'  # 使用增强的残差块
}
```

### 实战2：创建轻量级模型变体

```python
# 1. 创建深度可分离卷积组件
@register_component("DepthwiseSeparableConv")
class DepthwiseSeparableConv(nn.Module):
    """深度可分离卷积 - 减少参数量"""
    
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1):
        super().__init__()
        
        # 深度卷积
        self.depthwise = nn.Conv2d(
            in_channels, in_channels, kernel_size, 
            stride, padding, groups=in_channels, bias=False
        )
        
        # 点卷积
        self.pointwise = nn.Conv2d(in_channels, out_channels, 1, bias=False)
        
        self.bn1 = nn.BatchNorm2d(in_channels)
        self.bn2 = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU(inplace=True)
    
    def forward(self, x):
        x = self.depthwise(x)
        x = self.bn1(x)
        x = self.relu(x)
        
        x = self.pointwise(x)
        x = self.bn2(x)
        x = self.relu(x)
        
        return x

# 2. 创建轻量级FFC块
@register_component("LightweightFFCBlock")
class LightweightFFCBlock(nn.Module):
    """轻量级FFC块"""
    
    def __init__(self, channels, ratio_gin=0.25, ratio_gout=0.25):
        super().__init__()
        
        self.ratio_gin = ratio_gin
        self.ratio_gout = ratio_gout
        
        in_cg = int(channels * ratio_gin)
        in_cl = channels - in_cg
        out_cg = int(channels * ratio_gout)
        out_cl = channels - out_cg
        
        # 使用深度可分离卷积替代标准卷积
        self.local_conv = DepthwiseSeparableConv(in_cl, out_cl) if in_cl > 0 and out_cl > 0 else None
        
        # 简化的频域处理
        self.global_conv = nn.Conv2d(in_cg, out_cg, 1) if in_cg > 0 and out_cg > 0 else None
    
    def forward(self, x):
        # 简化的FFC处理逻辑
        if self.ratio_gin == 0:
            return self.local_conv(x) if self.local_conv else x
        elif self.ratio_gin == 1:
            return self.global_conv(x) if self.global_conv else x
        else:
            x_l, x_g = torch.split(x, [int(x.size(1) * (1 - self.ratio_gin)), 
                                      int(x.size(1) * self.ratio_gin)], dim=1)
            
            out_l = self.local_conv(x_l) if self.local_conv else None
            out_g = self.global_conv(x_g) if self.global_conv else None
            
            if out_l is None:
                return out_g
            elif out_g is None:
                return out_l
            else:
                return torch.cat([out_l, out_g], dim=1)

# 3. 创建轻量级模型
@register_model("LLIE_Light")
class LLIE_Light(BaseArchitecture):
    """轻量级LLIE模型"""
    
    def __init__(self, y_nf=8, f_nf=8, s_nf=16, num_blocks=4, **kwargs):
        super().__init__()
        
        # 使用更少的通道和块数
        self.luminance_processor = LuminanceMapProcessor(
            in_channels=3, out_channels=y_nf
        )
        
        # 使用轻量级FFC块
        self.first_process = nn.Sequential(
            nn.Conv2d(3, f_nf, 3, 1, 1),
            *[LightweightFFCBlock(f_nf) for _ in range(num_blocks)],
            nn.Conv2d(f_nf, f_nf, 3, 1, 1)
        )
        
        # 使用深度可分离卷积
        self.second_process = nn.Sequential(
            DepthwiseSeparableConv(3 + f_nf, s_nf),
            *[DepthwiseSeparableConv(s_nf, s_nf) for _ in range(num_blocks)],
            nn.Conv2d(s_nf, s_nf, 1)
        )
        
        self.fusion_conv = nn.Conv2d(y_nf + s_nf, 3, 1)
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        # 与原模型相同的前向传播逻辑
        luminance_features = self.luminance_processor(x)
        fourier_features = self.first_process(x)
        
        spatial_input = torch.cat([x, fourier_features], dim=1)
        spatial_features = self.second_process(spatial_input)
        
        fused_features = torch.cat([luminance_features, spatial_features], dim=1)
        enhanced = self.fusion_conv(fused_features)
        enhanced = self.sigmoid(enhanced)
        
        return enhanced

# 4. 比较模型参数量
def compare_model_complexity():
    """比较不同模型的复杂度"""
    
    # 原始模型
    original_model = MODELS.build('LLIE')
    original_params = sum(p.numel() for p in original_model.parameters())
    
    # 轻量级模型
    light_model = MODELS.build('LLIE_Light')
    light_params = sum(p.numel() for p in light_model.parameters())
    
    print(f"原始模型参数量: {original_params:,}")
    print(f"轻量级模型参数量: {light_params:,}")
    print(f"参数减少: {(1 - light_params/original_params)*100:.1f}%")
    
    # 测试推理速度
    import time
    
    dummy_input = torch.randn(1, 3, 256, 256)
    
    # 原始模型速度测试
    original_model.eval()
    with torch.no_grad():
        start_time = time.time()
        for _ in range(100):
            _ = original_model(dummy_input)
        original_time = time.time() - start_time
    
    # 轻量级模型速度测试
    light_model.eval()
    with torch.no_grad():
        start_time = time.time()
        for _ in range(100):
            _ = light_model(dummy_input)
        light_time = time.time() - start_time
    
    print(f"原始模型推理时间: {original_time:.3f}s")
    print(f"轻量级模型推理时间: {light_time:.3f}s")
    print(f"速度提升: {original_time/light_time:.1f}x")

# 运行比较
compare_model_complexity()
```

### 实战3：添加多尺度处理

```python
# 1. 创建多尺度特征提取器
@register_component("MultiScaleFeatureExtractor")
class MultiScaleFeatureExtractor(nn.Module):
    """多尺度特征提取器"""
    
    def __init__(self, in_channels, out_channels, scales=[1, 2, 4]):
        super().__init__()
        
        self.scales = scales
        self.convs = nn.ModuleList()
        
        for scale in scales:
            if scale == 1:
                # 原始尺度
                conv = nn.Conv2d(in_channels, out_channels // len(scales), 3, 1, 1)
            else:
                # 下采样后处理
                conv = nn.Sequential(
                    nn.AvgPool2d(scale, scale),
                    nn.Conv2d(in_channels, out_channels // len(scales), 3, 1, 1),
                    nn.Upsample(scale_factor=scale, mode='bilinear', align_corners=False)
                )
            self.convs.append(conv)
        
        # 特征融合
        self.fusion_conv = nn.Conv2d(out_channels, out_channels, 1)
    
    def forward(self, x):
        """多尺度特征提取"""
        features = []
        
        for conv in self.convs:
            feature = conv(x)
            features.append(feature)
        
        # 合并多尺度特征
        fused_features = torch.cat(features, dim=1)
        output = self.fusion_conv(fused_features)
        
        return output

# 2. 创建多尺度增强模型
@register_model("LLIE_MultiScale")
class LLIE_MultiScale(BaseArchitecture):
    """多尺度LLIE模型"""
    
    def __init__(self, y_nf=16, f_nf=16, s_nf=32, num_blocks=6, scales=[1, 2, 4], **kwargs):
        super().__init__()
        
        # 多尺度亮度处理
        self.luminance_processor = MultiScaleFeatureExtractor(
            in_channels=3, out_channels=y_nf, scales=scales
        )
        
        # 多尺度傅里叶处理
        self.first_process = nn.Sequential(
            MultiScaleFeatureExtractor(3, f_nf, scales),
            *[FFCBlock(f_nf) for _ in range(num_blocks)]
        )
        
        # 标准空间处理
        self.second_process = SecondProcessModel(
            in_channels=3 + f_nf, nf=s_nf, num_blocks=num_blocks
        )
        
        self.fusion_conv = nn.Conv2d(y_nf + s_nf, 3, 3, 1, 1)
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        # 多尺度处理
        luminance_features = self.luminance_processor(x)
        fourier_features = self.first_process(x)
        
        spatial_input = torch.cat([x, fourier_features], dim=1)
        spatial_features = self.second_process(spatial_input)
        
        fused_features = torch.cat([luminance_features, spatial_features], dim=1)
        enhanced = self.fusion_conv(fused_features)
        enhanced = self.sigmoid(enhanced)
        
        return enhanced
```

---

本指南提供了LLIE项目中网络架构修改的完整实战教程。通过理解注册表系统和核心组件，您可以轻松地修改、扩展和优化模型架构，以适应不同的研究需求和应用场景。

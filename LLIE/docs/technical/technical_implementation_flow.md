# LLIE框架训练-推理-评估技术实现流程详解

## 概述

本文档详细解释LLIE（Low-Light Image Enhancement）框架中训练、推理、评估三个阶段的代码实现流程，包括具体的函数调用、数据流转和关键代码片段。

---

## 1. 训练阶段 (Training Phase)

### 1.1 模型初始化和构建

#### 入口点：`src/llie/tasks/train_task.py`
```python
class TrainTask(BaseTask):
    def __init__(self, config: DictConfig):
        super().__init__(config)
        self.trainer = None
        
    def setup(self):
        """设置训练组件"""
        self._setup_data_loaders()  # 创建数据加载器
        self.trainer = ModernTrainer(self.config)  # 创建训练器
```

#### 核心训练器：`src/llie/engine/trainer.py`
```python
class ModernTrainer:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.device = torch.device(config.get('device', 'cuda'))
        
        # 按顺序初始化各个组件
        self._setup_model()              # 设置模型
        self._setup_loss()               # 设置损失函数
        self._setup_optimizer()          # 设置优化器
        self._setup_scheduler()          # 设置学习率调度器
        self._setup_metrics()            # 设置评估指标
        self._setup_training_utilities() # 设置训练工具（EMA、梯度裁剪等）
```

#### 模型构建流程：
```python
def _setup_model(self):
    """从配置中设置模型"""
    # 1. 解析配置
    model_config = OmegaConf.to_container(self.config['model'], resolve=True)
    
    # 2. 从注册表构建模型
    self.model = MODELS.build(model_config.copy())
    
    # 3. 移动到指定设备
    self.model = self.model.to(self.device)
    
    # 4. 多GPU支持
    if torch.cuda.device_count() > 1:
        self.model = nn.DataParallel(self.model)
```

### 1.2 数据加载器工作机制

#### 数据加载器创建：`src/llie/tasks/train_task.py`
```python
def _setup_data_loaders(self):
    """设置训练和验证数据加载器"""
    # 训练数据加载器
    dataset_dict = OmegaConf.to_container(self.config['dataset'], resolve=True)
    train_split_config = dataset_dict.pop('train', {})
    
    train_config = OmegaConf.create({
        **dataset_dict,
        **train_split_config,
        'split': 'train',
        'is_train': True
    })
    
    self.train_loader = create_dataloader(
        dataset_cfg=train_config,
        batch_size=self.config['trainer']['batch_size'],
        num_workers=self.config.get('num_workers', 4),
        is_train=True
    )
```

#### 数据加载器配置：`src/llie/data/dataloader.py`
```python
def create_dataloader(dataset_cfg, batch_size, num_workers=4, is_train=True):
    """创建优化的数据加载器"""
    dataset = DATASETS.build(dataset_cfg)
    
    return DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=is_train,           # 训练时打乱，验证时不打乱
        num_workers=num_workers,
        pin_memory=True,            # 加速GPU传输
        drop_last=is_train,         # 训练时丢弃最后不完整的批次
        persistent_workers=num_workers > 0,  # 保持worker进程
        prefetch_factor=2 if num_workers > 0 else 2,
    )
```

### 1.3 前向传播、损失计算、反向传播实现

#### 训练循环核心：`src/llie/engine/trainer.py`
```python
def train_epoch(self, train_loader: DataLoader, epoch: int) -> Dict[str, float]:
    """训练一个epoch"""
    self.model.train()
    self.train_metrics.reset()
    
    for batch_idx, batch in enumerate(train_loader):
        # 1. 数据准备
        inputs = batch['low_light'].to(self.device)
        targets = batch['normal_light'].to(self.device)
        
        # 2. 梯度清零
        self.optimizer.zero_grad()
        
        # 3. 前向传播（支持混合精度）
        if self.use_amp:
            with torch.cuda.amp.autocast():
                outputs = self.model(inputs)
                loss = self.criterion(outputs, targets)
            
            # 4. 反向传播（混合精度）
            self.scaler.scale(loss).backward()
            
            # 5. 梯度裁剪
            if self.grad_clipper is not None:
                self.scaler.unscale_(self.optimizer)
                grad_norm = self.grad_clipper(self.model)
            
            # 6. 优化器步进
            self.scaler.step(self.optimizer)
            self.scaler.update()
        else:
            # 标准精度训练
            outputs = self.model(inputs)
            loss = self.criterion(outputs, targets)
            
            loss.backward()
            
            if self.grad_clipper is not None:
                grad_norm = self.grad_clipper(self.model.parameters())
            
            self.optimizer.step()
        
        # 7. EMA更新
        if self.ema is not None:
            self.ema.update()
        
        # 8. 指标更新
        self.train_metrics.update(outputs, targets)
```

### 1.4 训练循环中的关键步骤

#### 主训练循环：`src/llie/engine/trainer.py`
```python
def train(self, train_loader: DataLoader, val_loader: Optional[DataLoader] = None, 
          num_epochs: Optional[int] = None, resume_from: Optional[str] = None):
    """主训练循环"""
    
    # 1. 恢复检查点（如果指定）
    if resume_from is not None:
        self._load_checkpoint(resume_from)
    
    # 2. 训练循环
    for epoch in range(self.current_epoch, num_epochs):
        self.current_epoch = epoch
        
        # 3. 训练一个epoch
        train_metrics = self.train_epoch(train_loader, epoch)
        
        # 4. 验证（如果有验证集）
        val_metrics = {}
        if val_loader is not None:
            val_metrics = self.validate(val_loader, epoch)
        
        # 5. 学习率调度
        if self.scheduler is not None:
            if isinstance(self.scheduler, torch.optim.lr_scheduler.ReduceLROnPlateau):
                metric_to_monitor = self.config['trainer'].get('monitor_metric', 'loss')
                metric_value = val_metrics.get(metric_to_monitor, train_metrics.get('loss', 0))
                self.scheduler.step(metric_value)
            else:
                self.scheduler.step()
        
        # 6. 记录结果
        self._log_epoch_results(epoch, train_metrics, val_metrics)
        
        # 7. 保存检查点
        if epoch % self.config['trainer'].get('save_freq', 10) == 0:
            self._save_checkpoint(epoch, train_metrics, val_metrics)
        
        # 8. 早停检查
        if self._should_early_stop(val_metrics):
            logger.info("Early stopping triggered")
            break
    
    # 9. 保存最终模型
    self._save_checkpoint(epoch, train_metrics, val_metrics, is_final=True)
```

---

## 2. 推理阶段 (Inference Phase)

### 2.1 训练完成后切换到评估模式

#### 模式切换：
```python
# 在训练完成后
model.eval()  # 切换到评估模式，禁用dropout和batch normalization的训练行为

# 使用EMA模型（如果启用）
if self.ema is not None:
    self.ema.apply_shadow()  # 应用EMA权重
```

### 2.2 模型检查点加载机制

#### 检查点保存：`src/llie/engine/trainer.py`
```python
def _save_checkpoint(self, epoch: int, train_metrics: Dict, val_metrics: Dict, is_final: bool = False):
    """保存检查点"""
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': self.model.state_dict(),
        'optimizer_state_dict': self.optimizer.state_dict(),
        'train_metrics': train_metrics,
        'val_metrics': val_metrics,
        'config': self.config,
    }
    
    # 保存调度器状态
    if self.scheduler is not None:
        checkpoint['scheduler_state_dict'] = self.scheduler.state_dict()
    
    # 保存EMA状态
    if self.ema is not None:
        checkpoint['ema_state_dict'] = self.ema.shadow
    
    # 保存到文件
    if is_final:
        path = self.output_dir / "final_model.pth"
    else:
        path = self.output_dir / f"checkpoint_epoch_{epoch:03d}.pth"
    
    torch.save(checkpoint, path)
```

#### 检查点加载：`src/llie/tasks/evaluate_task.py`
```python
def load_checkpoint(checkpoint_path, model, device):
    """加载检查点"""
    # 1. 加载检查点文件
    checkpoint = torch.load(checkpoint_path, map_location=device, weights_only=False)
    
    # 2. 处理不同的检查点格式
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    else:
        state_dict = checkpoint
    
    # 3. 加载模型权重
    model.load_state_dict(state_dict)
    
    return model

### 2.3 单张图像推理过程

#### 推理任务：`src/llie/tasks/inference_task.py`
```python
def run(cfg: DictConfig) -> None:
    """运行推理任务"""
    # 1. 设备和路径设置
    device = torch.device(cfg.device)
    input_path = Path(cfg.get('input_path', './data/test'))
    output_dir = Path(cfg.get('output_dir', './outputs/inference'))

    # 2. 构建和加载模型
    model = MODELS.build(cfg.model)
    model.to(device)

    # 3. 加载检查点
    if cfg.get('checkpoint_path'):
        checkpoint = torch.load(cfg.checkpoint_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])

    model.eval()

    # 4. 图像预处理
    transform = T.Compose([
        T.Resize((cfg.get('image_size', 256), cfg.get('image_size', 256))),
        T.ToTensor(),
    ])

    # 5. 推理循环
    with torch.no_grad():
        for img_path in image_paths:
            # 5.1 加载和预处理图像
            img = Image.open(img_path).convert("RGB")
            tensor_img = transform(img).unsqueeze(0).to(device)

            # 5.2 模型前向传播
            enhanced_img = model(tensor_img)

            # 5.3 后处理和保存
            output_path = output_dir / f"enhanced_{img_path.stem}.png"
            save_tensor_as_image(enhanced_img.squeeze(0), output_path)
```

### 2.4 批量推理实现

#### 评估任务中的批量推理：`src/llie/tasks/evaluate_task.py`
```python
def batch_inference(model, val_loader, device):
    """批量推理实现"""
    model.eval()
    results = []

    with torch.no_grad():
        for i, batch in enumerate(tqdm(val_loader, desc="Evaluating")):
            # 1. 数据准备
            inputs = batch['low_light'].to(device)
            targets = batch['normal_light'].to(device)
            img_names = batch.get('name', [f'img_{i:04d}'])

            # 2. 批量前向传播
            outputs = model(inputs)

            # 3. 处理每个样本
            for j in range(outputs.size(0)):
                enhanced = outputs[j]
                target = targets[j]
                name = img_names[j] if j < len(img_names) else f'img_{i:04d}_{j}'

                # 4. 计算单样本指标
                individual_metrics = compute_individual_metrics(enhanced, target)
                results.append({'name': name, **individual_metrics})

                # 5. 保存增强图像
                save_enhanced_image(enhanced, output_dir / "images" / f"{name}.png")

    return results
```

---

## 3. 评估阶段 (Evaluation Phase)

### 3.1 各种指标的具体计算实现

#### PSNR指标：`src/llie/metrics.py`
```python
@register_metric("psnr")
class PSNR(nn.Module):
    """峰值信噪比计算"""

    def __init__(self, max_val: float = 1.0, reduction: str = "mean"):
        super().__init__()
        self.max_val = max_val
        self.reduction = reduction

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        计算PSNR
        Args:
            pred: 预测图像 [B, C, H, W]，范围[0, 1]
            target: 目标图像 [B, C, H, W]，范围[0, 1]
        """
        # 1. 计算MSE
        mse = F.mse_loss(pred, target, reduction='none')
        mse = mse.view(mse.size(0), -1).mean(dim=1)

        # 2. 计算PSNR
        psnr = 20 * torch.log10(self.max_val / torch.sqrt(mse + 1e-8))

        # 3. 应用reduction
        if self.reduction == "mean":
            return psnr.mean()
        elif self.reduction == "sum":
            return psnr.sum()
        else:
            return psnr
```

#### SSIM指标：`src/llie/metrics.py`
```python
@register_metric("ssim")
class SSIM(nn.Module):
    """结构相似性指数计算"""

    def __init__(self, window_size: int = 11, sigma: float = 1.5,
                 data_range: float = 1.0, size_average: bool = True):
        super().__init__()
        self.window_size = window_size
        self.sigma = sigma
        self.data_range = data_range
        self.size_average = size_average

        # 创建高斯窗口
        self.register_buffer('window', self._create_window(window_size, sigma))

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """计算SSIM"""
        # 1. 计算均值
        mu1 = F.conv2d(pred, self.window, padding=self.window_size//2, groups=pred.size(1))
        mu2 = F.conv2d(target, self.window, padding=self.window_size//2, groups=target.size(1))

        mu1_sq = mu1.pow(2)
        mu2_sq = mu2.pow(2)
        mu1_mu2 = mu1 * mu2

        # 2. 计算方差和协方差
        sigma1_sq = F.conv2d(pred * pred, self.window, padding=self.window_size//2, groups=pred.size(1)) - mu1_sq
        sigma2_sq = F.conv2d(target * target, self.window, padding=self.window_size//2, groups=target.size(1)) - mu2_sq
        sigma12 = F.conv2d(pred * target, self.window, padding=self.window_size//2, groups=pred.size(1)) - mu1_mu2

        # 3. 计算SSIM
        C1 = (0.01 * self.data_range) ** 2
        C2 = (0.03 * self.data_range) ** 2

        numerator = (2 * mu1_mu2 + C1) * (2 * sigma12 + C2)
        denominator = (mu1_sq + mu2_sq + C1) * (sigma1_sq + sigma2_sq + C2)

        ssim_map = numerator / denominator
        return ssim_map.mean()
```

#### LPIPS指标：`src/llie/metrics.py`
```python
@register_metric("lpips")
class LPIPS(nn.Module):
    """感知损失计算"""

    def __init__(self, network: str = "alex", spatial: bool = False):
        super().__init__()
        self.lpips_fn = lpips.LPIPS(net=network, spatial=spatial)

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        计算LPIPS
        Args:
            pred: 预测图像 [B, C, H, W]，范围[0, 1]
            target: 目标图像 [B, C, H, W]，范围[0, 1]
        """
        # LPIPS期望输入范围为[-1, 1]
        pred_norm = pred * 2.0 - 1.0
        target_norm = target * 2.0 - 1.0

        return self.lpips_fn(pred_norm, target_norm).mean()
```

#### MAE指标：`src/llie/metrics.py`
```python
@register_metric("mae")
class MAE(nn.Module):
    """平均绝对误差"""

    def __init__(self, reduction: str = "mean"):
        super().__init__()
        self.reduction = reduction

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        return F.l1_loss(pred, target, reduction=self.reduction)
```

### 3.2 模型复杂度分析计算方法

#### 参数量计算：`src/llie/utils/model_analysis.py`
```python
def get_parameter_count(self) -> Dict[str, Any]:
    """计算模型参数量"""
    total_params = 0
    trainable_params = 0

    for param in self.model.parameters():
        total_params += param.numel()
        if param.requires_grad:
            trainable_params += param.numel()

    model_size_mb = total_params * 4 / (1024 * 1024)  # 假设float32

    return {
        'total_parameters': total_params,
        'trainable_parameters': trainable_params,
        'non_trainable_parameters': total_params - trainable_params,
        'model_size_mb': model_size_mb
    }
```

#### FLOPS计算：`src/llie/utils/model_analysis.py`
```python
def get_flops(self, batch_size: int = 1) -> Dict[str, Any]:
    """计算FLOPS"""
    from thop import profile, clever_format

    # 创建输入张量
    input_tensor = torch.randn(batch_size, *self.input_size).to(self.device)

    try:
        # 使用thop库计算FLOPS
        flops, params = profile(self.model, inputs=(input_tensor,), verbose=False)
        flops_formatted, params_formatted = clever_format([flops, params], "%.3f")

        return {
            'flops': flops,
            'flops_formatted': flops_formatted,
            'params': params,
            'params_formatted': params_formatted,
            'flops_per_pixel': flops / (self.input_size[1] * self.input_size[2]),
        }
    except Exception as e:
        logger.error(f"FLOPS calculation failed: {e}")
        return {'flops': 0, 'flops_formatted': 'Error'}
```

### 3.3 推理时间和内存使用测量

#### 推理时间测量：`src/llie/utils/model_analysis.py`
```python
def measure_inference_time(self, num_runs: int = 100) -> Dict[str, float]:
    """测量推理时间"""
    self.model.eval()

    # 预热
    dummy_input = torch.randn(1, *self.input_size).to(self.device)
    for _ in range(10):
        with torch.no_grad():
            _ = self.model(dummy_input)

    # 同步GPU
    if self.device.type == 'cuda':
        torch.cuda.synchronize()

    # 测量时间
    times = []
    for _ in range(num_runs):
        start_time = time.time()

        with torch.no_grad():
            _ = self.model(dummy_input)

        if self.device.type == 'cuda':
            torch.cuda.synchronize()

        end_time = time.time()
        times.append(end_time - start_time)

    # 统计结果
    times = np.array(times)
    return {
        'mean_time': np.mean(times),
        'std_time': np.std(times),
        'min_time': np.min(times),
        'max_time': np.max(times),
        'fps': 1.0 / np.mean(times)
    }
```

#### 内存使用测量：`src/llie/utils/model_analysis.py`
```python
def get_memory_usage(self) -> Dict[str, float]:
    """测量GPU内存使用"""
    if not torch.cuda.is_available():
        return {'initial_memory_mb': 0, 'peak_memory_mb': 0, 'current_memory_mb': 0}

    # 清空缓存
    torch.cuda.empty_cache()
    initial_memory = torch.cuda.memory_allocated() / (1024 * 1024)

    # 执行推理
    dummy_input = torch.randn(1, *self.input_size).to(self.device)
    with torch.no_grad():
        _ = self.model(dummy_input)

    peak_memory = torch.cuda.max_memory_allocated() / (1024 * 1024)
    current_memory = torch.cuda.memory_allocated() / (1024 * 1024)

    return {
        'initial_memory_mb': initial_memory,
        'peak_memory_mb': peak_memory,
        'current_memory_mb': current_memory,
        'memory_increase_mb': current_memory - initial_memory
    }
```

### 3.4 结果汇总和保存逻辑

#### 指标跟踪器：`src/llie/metrics.py`
```python
class MetricTracker:
    """指标跟踪和计算"""

    def __init__(self, metrics: Dict[str, nn.Module]):
        self.metrics = metrics
        self.reset()

    def update(self, pred: torch.Tensor, target: torch.Tensor) -> Dict[str, float]:
        """更新指标"""
        results = {}

        with torch.no_grad():
            for name, metric in self.metrics.items():
                try:
                    value = metric(pred, target)
                    if isinstance(value, torch.Tensor):
                        value = value.item()

                    self.values[name].append(value)
                    results[name] = value

                except Exception as e:
                    print(f"Warning: Failed to compute {name}: {e}")
                    results[name] = float('nan')

        self.count += 1
        return results

    def compute(self) -> Dict[str, float]:
        """计算平均指标"""
        return {
            name: np.mean(values) if values else 0.0
            for name, values in self.values.items()
        }
```

#### 结果保存：`src/llie/tasks/evaluate_task.py`
```python
def save_evaluation_results(results, avg_metrics, model_analysis, output_dir):
    """保存评估结果"""

    # 1. 保存详细指标到CSV
    df = pd.DataFrame(results)
    detailed_path = output_dir / "detailed_metrics.csv"
    df.to_csv(detailed_path, index=False)

    # 2. 保存汇总指标到CSV
    summary_data = {**avg_metrics}

    # 添加模型复杂度指标
    params = model_analysis['parameters']
    flops = model_analysis['flops']
    timing = model_analysis['timing']

    summary_data.update({
        'total_parameters': params['total_parameters'],
        'trainable_parameters': params['trainable_parameters'],
        'model_size_mb': params['model_size_mb'],
        'flops': flops['flops'],
        'flops_formatted': flops['flops_formatted'],
        'inference_time_ms': timing['mean_time'] * 1000,
        'fps': timing['fps']
    })

    summary_df = pd.DataFrame([summary_data])
    summary_path = output_dir / "summary_metrics.csv"
    summary_df.to_csv(summary_path, index=False)

    # 3. 保存模型分析到JSON
    import json
    analysis_path = output_dir / "model_analysis.json"
    with open(analysis_path, 'w') as f:
        json.dump(model_analysis, f, indent=2)

    logger.info(f"Detailed metrics saved to {detailed_path}")
    logger.info(f"Summary metrics saved to {summary_path}")
    logger.info(f"Model analysis saved to {analysis_path}")
```

---

## 总结

LLIE框架的训练-推理-评估流程通过模块化设计实现了高度的可配置性和可扩展性：

1. **训练阶段**：通过ModernTrainer类统一管理模型、优化器、调度器等组件，支持混合精度、EMA、梯度裁剪等高级特性
2. **推理阶段**：支持单张图像和批量推理，通过检查点机制实现模型状态的保存和恢复
3. **评估阶段**：实现了完整的指标计算体系，包括图像质量指标和模型复杂度分析，结果以多种格式保存

整个流程通过注册机制实现了组件的动态加载，通过Hydra配置系统实现了灵活的参数管理，为低光图像增强研究提供了完整的实验框架。
```

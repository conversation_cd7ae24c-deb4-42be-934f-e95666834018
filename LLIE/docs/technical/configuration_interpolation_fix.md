# Hydra配置插值修复说明

## 问题描述

在LLIE项目中，我们遇到了Hydra配置插值错误：

```
InterpolationKeyError: Interpolation key 'architecture.y_nf' not found
    full_key: model.components.luminance_map.base_channel
```

## 问题原因

这个错误是由于Hydra配置插值的作用域问题导致的。在`configs/model/dmfourllie.yaml`中，`components`部分试图引用`architecture.y_nf`，但是在`components`的上下文中，`architecture`不在同一个作用域内。

## 解决方案

### 修复前的配置

```yaml
# configs/model/dmfourllie.yaml (错误的插值引用)
components:
  luminance_map:
    type: LuminanceMap
    base_channel: ${architecture.y_nf}  # ❌ 错误：找不到architecture.y_nf
  
  fourier_stage:
    type: FirstProcessModel
    nf: ${architecture.f_nf}  # ❌ 错误：找不到architecture.f_nf
```

### 修复后的配置

```yaml
# configs/model/dmfourllie.yaml (正确的插值引用)
components:
  luminance_map:
    type: LuminanceMap
    base_channel: ${model.architecture.y_nf}  # ✅ 正确：完整路径引用
  
  fourier_stage:
    type: FirstProcessModel
    nf: ${model.architecture.f_nf}  # ✅ 正确：完整路径引用
```

## 修复的具体更改

1. **亮度图模块**：`${architecture.y_nf}` → `${model.architecture.y_nf}`
2. **傅里叶阶段模块**：`${architecture.f_nf}` → `${model.architecture.f_nf}`
3. **多阶段空间处理模块**：
   - `${architecture.s_nf}` → `${model.architecture.s_nf}`
   - `${architecture.num_blocks}` → `${model.architecture.num_blocks}`
   - `${architecture.input_channels}` → `${model.architecture.input_channels}`

## 训练器配置修复

同时修复了训练器配置中的类似问题：

```yaml
# configs/trainer/default_trainer.yaml
scheduler:
  type: CosineAnnealingLR
  T_max: ${trainer.max_epochs}  # ✅ 正确：完整路径引用
  eta_min: 1e-6
```

## 验证修复效果

修复后，运行训练命令可以看到配置正确加载：

```bash
python3 run.py task=train
```

成功输出：
```
2025-07-29 16:24:05 | INFO | Model: LLIE
2025-07-29 16:24:05 | INFO | Total parameters: 952,823
2025-07-29 16:24:05 | INFO | Loss function: CharbonnierLoss
2025-07-29 16:24:05 | INFO | Optimizer: Adam
2025-07-29 16:24:05 | INFO | Scheduler: CosineAnnealingLR
```

## 最佳实践

在Hydra配置中使用插值时，建议：

1. **使用完整路径**：始终使用从根配置开始的完整路径
2. **明确作用域**：确保引用的配置项在当前作用域内可访问
3. **测试配置**：修改配置后及时测试以确保插值正确解析

## 相关文件

- `configs/model/dmfourllie.yaml` - 模型配置文件
- `configs/trainer/default_trainer.yaml` - 训练器配置文件
- `src/llie/engine/trainer.py` - 训练器实现（修复了OmegaConf struct模式问题）

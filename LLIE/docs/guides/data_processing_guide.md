# 数据处理完整指南

本指南详细介绍LLIE项目中数据处理的各个环节，包括数据集获取、组织、预处理、增强和调试等内容。

## 📋 目录

1. [数据集概述](#数据集概述)
2. [数据集获取和下载](#数据集获取和下载)
3. [数据组织和验证](#数据组织和验证)
4. [数据预处理流程](#数据预处理流程)
5. [数据增强策略](#数据增强策略)
6. [数据加载器配置](#数据加载器配置)
7. [数据质量检查](#数据质量检查)
8. [常见问题和解决方案](#常见问题和解决方案)

## 🎯 数据集概述

### LOL数据集系列

**LOL (Low-Light Object Recognition) 数据集**是低光图像增强领域的标准数据集，包含配对的低光和正常光照图像。

#### LOLv1 数据集
- **训练集**: 485对图像
- **测试集**: 15对图像
- **图像尺寸**: 不固定，通常在400x600左右
- **格式**: PNG
- **特点**: 真实场景拍摄，光照条件多样

#### LOLv2 数据集
- **Real_captured**: 真实拍摄数据
  - 训练集: 485对图像
  - 测试集: 100对图像
- **Synthetic**: 合成数据
  - 训练集: 1000对图像
  - 测试集: 100对图像
- **改进**: 更高质量的图像对，更好的配准

### 数据集特点分析

```python
# 数据集统计分析脚本
import os
import numpy as np
from PIL import Image
from pathlib import Path
import matplotlib.pyplot as plt

def analyze_dataset(data_root):
    """分析数据集的基本统计信息"""
    
    stats = {
        'train_low': [], 'train_normal': [],
        'test_low': [], 'test_normal': []
    }
    
    # 收集图像尺寸和亮度信息
    for split in ['Train', 'Test']:
        for light_type in ['Low', 'Normal']:
            path = Path(data_root) / split / light_type
            key = f"{split.lower()}_{light_type.lower()}"
            
            for img_path in path.glob('*.png'):
                img = Image.open(img_path)
                img_array = np.array(img)
                
                stats[key].append({
                    'size': img.size,
                    'mean_brightness': np.mean(img_array),
                    'std_brightness': np.std(img_array)
                })
    
    return stats

# 使用示例
stats = analyze_dataset('data/LOLv2/Real_captured')
print("数据集统计完成")
```

## 📥 数据集获取和下载

### 官方下载地址

#### LOLv1
```bash
# 官方GitHub仓库
git clone https://github.com/daooshee/BMVC2018website.git
# 或直接下载数据集文件
wget https://github.com/daooshee/BMVC2018website/releases/download/v1.0/LOL_dataset.zip
```

#### LOLv2
```bash
# 官方GitHub仓库
git clone https://github.com/flyywh/CVPR-2020-Semi-Low-Light.git
# 或从Google Drive下载（需要科学上网）
# https://drive.google.com/drive/folders/1dzuLCk9_gE2bFF222n3-7GVUlSVHpMYC
```

### 自动下载脚本

```python
# scripts/download_data.py
import os
import requests
import zipfile
from pathlib import Path
from tqdm import tqdm

def download_file(url, filename):
    """下载文件并显示进度条"""
    response = requests.get(url, stream=True)
    total_size = int(response.headers.get('content-length', 0))
    
    with open(filename, 'wb') as file, tqdm(
        desc=filename,
        total=total_size,
        unit='B',
        unit_scale=True,
        unit_divisor=1024,
    ) as pbar:
        for chunk in response.iter_content(chunk_size=8192):
            size = file.write(chunk)
            pbar.update(size)

def setup_lol_dataset():
    """设置LOL数据集"""
    data_dir = Path('data')
    data_dir.mkdir(exist_ok=True)
    
    print("🔄 开始下载LOL数据集...")
    
    # 这里需要替换为实际的下载链接
    # download_file('LOL_DATASET_URL', 'data/LOL_dataset.zip')
    
    print("✅ 数据集下载完成")
    
    # 解压数据集
    print("🔄 解压数据集...")
    with zipfile.ZipFile('data/LOL_dataset.zip', 'r') as zip_ref:
        zip_ref.extractall('data/')
    
    print("✅ 数据集设置完成")

if __name__ == "__main__":
    setup_lol_dataset()
```

### 手动下载指南

如果自动下载失败，请按照以下步骤手动下载：

1. **访问官方网站**
   - LOLv1: https://daooshee.github.io/BMVC2018website/
   - LOLv2: https://github.com/flyywh/CVPR-2020-Semi-Low-Light

2. **下载数据集文件**
   - 通常为ZIP格式
   - 文件大小约1-3GB

3. **解压到指定目录**
   ```bash
   unzip LOL_dataset.zip -d data/
   ```

## 📁 数据组织和验证

### 标准目录结构

```
data/
└── LOLv2/
    └── Real_captured/
        ├── Train/
        │   ├── Low/           # 训练用低光图像
        │   │   ├── 1.png
        │   │   ├── 2.png
        │   │   └── ...
        │   └── Normal/        # 训练用正常光照图像
        │       ├── 1.png
        │       ├── 2.png
        │       └── ...
        └── Test/
            ├── Low/           # 测试用低光图像
            │   ├── 1.png
            │   ├── 2.png
            │   └── ...
            └── Normal/        # 测试用正常光照图像
                ├── 1.png
                ├── 2.png
                └── ...
```

### 数据验证脚本

```python
# scripts/validate_dataset.py
from pathlib import Path
import sys

def validate_dataset_structure(data_root):
    """验证数据集目录结构"""
    
    data_root = Path(data_root)
    required_dirs = [
        'Train/Low', 'Train/Normal',
        'Test/Low', 'Test/Normal'
    ]
    
    print("🔍 验证数据集结构...")
    
    missing_dirs = []
    for dir_path in required_dirs:
        full_path = data_root / dir_path
        if not full_path.exists():
            missing_dirs.append(str(full_path))
        else:
            # 统计图像数量
            image_count = len(list(full_path.glob('*.png'))) + len(list(full_path.glob('*.jpg')))
            print(f"✅ {dir_path}: {image_count} 张图像")
    
    if missing_dirs:
        print("❌ 缺少以下目录:")
        for missing in missing_dirs:
            print(f"   - {missing}")
        return False
    
    print("✅ 数据集结构验证通过")
    return True

def validate_image_pairs(data_root):
    """验证图像配对"""
    
    data_root = Path(data_root)
    
    for split in ['Train', 'Test']:
        low_dir = data_root / split / 'Low'
        normal_dir = data_root / split / 'Normal'
        
        low_files = set(f.name for f in low_dir.glob('*.png'))
        normal_files = set(f.name for f in normal_dir.glob('*.png'))
        
        # 检查配对
        missing_normal = low_files - normal_files
        missing_low = normal_files - low_files
        
        if missing_normal:
            print(f"⚠️ {split}集中缺少正常光照图像: {missing_normal}")
        
        if missing_low:
            print(f"⚠️ {split}集中缺少低光图像: {missing_low}")
        
        paired_count = len(low_files & normal_files)
        print(f"✅ {split}集配对图像: {paired_count} 对")

if __name__ == "__main__":
    data_root = sys.argv[1] if len(sys.argv) > 1 else 'data/LOLv2/Real_captured'
    
    if validate_dataset_structure(data_root):
        validate_image_pairs(data_root)
```

### 数据集转换工具

如果您的数据集格式不同，可以使用转换工具：

```python
# scripts/convert_dataset.py
import shutil
from pathlib import Path

def convert_dataset_format(source_dir, target_dir):
    """转换数据集格式到标准结构"""
    
    source_dir = Path(source_dir)
    target_dir = Path(target_dir)
    
    # 创建目标目录
    for split in ['Train', 'Test']:
        for light_type in ['Low', 'Normal']:
            (target_dir / split / light_type).mkdir(parents=True, exist_ok=True)
    
    # 根据您的数据集结构进行转换
    # 这里是一个示例，需要根据实际情况修改
    
    print("🔄 转换数据集格式...")
    
    # 示例：假设原始数据集结构为 source_dir/low/ 和 source_dir/high/
    if (source_dir / 'low').exists() and (source_dir / 'high').exists():
        # 复制文件到新结构
        low_files = list((source_dir / 'low').glob('*'))
        high_files = list((source_dir / 'high').glob('*'))
        
        # 分割训练和测试集（80/20）
        train_count = int(len(low_files) * 0.8)
        
        for i, (low_file, high_file) in enumerate(zip(low_files, high_files)):
            if i < train_count:
                split = 'Train'
            else:
                split = 'Test'
            
            # 复制文件
            shutil.copy2(low_file, target_dir / split / 'Low' / low_file.name)
            shutil.copy2(high_file, target_dir / split / 'Normal' / high_file.name)
    
    print("✅ 数据集格式转换完成")

# 使用示例
# convert_dataset_format('path/to/original/dataset', 'data/LOLv2/Real_captured')
```

## 🔄 数据预处理流程

### 图像预处理管道

LLIE项目使用Albumentations库进行数据预处理，支持配对图像的同步变换。

#### 基础预处理

```python
# src/llie/data/transforms.py 中的关键部分
import albumentations as A
from albumentations.pytorch import ToTensorV2

def get_basic_transforms(image_size=None, normalize=True):
    """获取基础预处理变换"""
    
    transforms = []
    
    # 尺寸调整（如果指定）
    if image_size:
        transforms.append(
            A.Resize(height=image_size[0], width=image_size[1], 
                    interpolation=cv2.INTER_LINEAR)
        )
    
    # 转换为张量
    transforms.append(ToTensorV2())
    
    # 标准化
    if normalize:
        transforms.append(
            A.Normalize(
                mean=[0.485, 0.456, 0.406],  # ImageNet统计值
                std=[0.229, 0.224, 0.225],
                max_pixel_value=255.0
            )
        )
    
    return A.Compose(transforms, additional_targets={'normal': 'image'})
```

#### 训练时预处理

```python
def get_training_transforms(image_size=None):
    """获取训练时的数据增强变换"""
    
    transforms = [
        # 几何变换
        A.RandomHorizontalFlip(p=0.5),
        A.RandomRotation(limit=10, p=0.3),
        
        # 颜色变换
        A.ColorJitter(
            brightness=0.1,
            contrast=0.1,
            saturation=0.1,
            hue=0.05,
            p=0.3
        ),
        
        # 噪声和模糊
        A.OneOf([
            A.GaussNoise(var_limit=(10, 50), p=0.5),
            A.GaussianBlur(blur_limit=3, p=0.5),
        ], p=0.2),
    ]
    
    # 添加基础变换
    if image_size:
        transforms.insert(0, A.Resize(height=image_size[0], width=image_size[1]))
    
    transforms.extend([
        ToTensorV2(),
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    return A.Compose(transforms, additional_targets={'normal': 'image'})
```

### 配置文件中的预处理设置

在 `configs/dataset/LOLv2_Real.yaml` 中配置预处理：

```yaml
# 训练时的数据增强变换
train:
  transforms:
    # 随机水平翻转
    - type: "RandomHorizontalFlip"
      p: 0.5
    
    # 随机旋转
    - type: "RandomRotation"
      degrees: 10
      p: 0.3
    
    # 颜色抖动
    - type: "ColorJitter"
      brightness: 0.1
      contrast: 0.1
      saturation: 0.1
      hue: 0.05
      p: 0.3
    
    # 转换为张量
    - type: "ToTensor"
    
    # 标准化
    - type: "Normalize"
      mean: [0.485, 0.456, 0.406]
      std: [0.229, 0.224, 0.225]

# 验证时的变换（不进行数据增强）
eval:
  transforms:
    - type: "ToTensor"
    - type: "Normalize"
      mean: [0.485, 0.456, 0.406]
      std: [0.229, 0.224, 0.225]
```

## 🎨 数据增强策略

### 几何变换

```python
# 几何变换示例
geometric_transforms = [
    # 水平翻转 - 增加数据多样性
    A.HorizontalFlip(p=0.5),
    
    # 垂直翻转 - 谨慎使用，可能改变光照方向
    A.VerticalFlip(p=0.1),
    
    # 旋转 - 轻微旋转增强鲁棒性
    A.Rotate(limit=15, p=0.3),
    
    # 缩放和裁剪
    A.RandomResizedCrop(height=256, width=256, scale=(0.8, 1.0), p=0.3),
    
    # 透视变换
    A.Perspective(scale=(0.05, 0.1), p=0.2),
]
```

### 光照和颜色变换

```python
# 光照和颜色变换
photometric_transforms = [
    # 亮度调整 - 模拟不同光照条件
    A.RandomBrightness(limit=0.1, p=0.3),
    
    # 对比度调整
    A.RandomContrast(limit=0.1, p=0.3),
    
    # 饱和度调整
    A.HueSaturationValue(
        hue_shift_limit=10,
        sat_shift_limit=15,
        val_shift_limit=10,
        p=0.3
    ),
    
    # Gamma校正
    A.RandomGamma(gamma_limit=(80, 120), p=0.2),
    
    # 通道混洗
    A.ChannelShuffle(p=0.1),
]
```

### 噪声和质量降级

```python
# 噪声和质量降级
degradation_transforms = [
    # 高斯噪声
    A.GaussNoise(var_limit=(10, 50), p=0.2),
    
    # 运动模糊
    A.MotionBlur(blur_limit=3, p=0.1),
    
    # 高斯模糊
    A.GaussianBlur(blur_limit=3, p=0.1),
    
    # JPEG压缩
    A.ImageCompression(quality_lower=85, quality_upper=100, p=0.2),
]
```

### 自定义增强策略

```python
# 针对低光图像的自定义增强
class LowLightAugmentation:
    """低光图像专用数据增强"""
    
    def __init__(self, p=0.5):
        self.p = p
        
        # 轻量级增强 - 保持低光特性
        self.light_augment = A.Compose([
            A.RandomHorizontalFlip(p=0.5),
            A.RandomRotation(limit=5, p=0.3),
            A.ColorJitter(brightness=0.05, contrast=0.05, p=0.2),
        ], additional_targets={'normal': 'image'})
        
        # 强增强 - 增加多样性
        self.strong_augment = A.Compose([
            A.RandomHorizontalFlip(p=0.5),
            A.RandomRotation(limit=15, p=0.5),
            A.ColorJitter(brightness=0.1, contrast=0.1, saturation=0.1, p=0.4),
            A.OneOf([
                A.GaussNoise(var_limit=(10, 30), p=0.5),
                A.GaussianBlur(blur_limit=3, p=0.5),
            ], p=0.3),
        ], additional_targets={'normal': 'image'})
    
    def __call__(self, low_image, normal_image):
        """应用增强"""
        import random
        
        if random.random() < self.p:
            # 随机选择增强强度
            augment = self.strong_augment if random.random() < 0.3 else self.light_augment
            
            result = augment(image=low_image, normal=normal_image)
            return result['image'], result['normal']
        
        return low_image, normal_image
```

## ⚙️ 数据加载器配置

### 基础数据加载器

```python
# src/llie/data/dataloader.py
from torch.utils.data import DataLoader
from .dataset import LOLDataset

def create_dataloader(dataset_cfg, batch_size, num_workers=4, is_train=True):
    """创建数据加载器"""
    
    # 创建数据集
    dataset = LOLDataset(**dataset_cfg)
    
    # 配置数据加载器
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=is_train,  # 训练时打乱，验证时不打乱
        num_workers=num_workers,
        pin_memory=True,  # 加速GPU传输
        drop_last=is_train,  # 训练时丢弃最后不完整的批次
        persistent_workers=num_workers > 0,  # 保持worker进程
    )
    
    return dataloader
```

### 高级配置选项

```yaml
# 在配置文件中设置数据加载参数
dataloader:
  batch_size: 16
  num_workers: 8
  pin_memory: true
  drop_last: true
  persistent_workers: true
  
  # 预取因子（PyTorch 1.7+）
  prefetch_factor: 2
  
  # 内存映射文件
  memory_map: false
  
  # 数据缓存
  cache_images: false
  max_cache_size: 1000
```

### 性能优化

```python
# 数据加载性能优化
class OptimizedDataLoader:
    """优化的数据加载器"""
    
    def __init__(self, dataset, batch_size, num_workers=4):
        self.dataset = dataset
        self.batch_size = batch_size
        self.num_workers = num_workers
        
        # 根据系统配置自动调整参数
        self._optimize_settings()
    
    def _optimize_settings(self):
        """自动优化设置"""
        import psutil
        import torch
        
        # 根据CPU核心数调整worker数量
        cpu_count = psutil.cpu_count(logical=False)
        self.num_workers = min(self.num_workers, cpu_count)
        
        # 根据内存大小调整批次大小
        memory_gb = psutil.virtual_memory().total / (1024**3)
        if memory_gb < 8:
            self.batch_size = min(self.batch_size, 4)
        
        # GPU内存检查
        if torch.cuda.is_available():
            gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            if gpu_memory_gb < 6:
                self.batch_size = min(self.batch_size, 8)
    
    def create_loader(self):
        """创建优化的数据加载器"""
        return DataLoader(
            self.dataset,
            batch_size=self.batch_size,
            num_workers=self.num_workers,
            pin_memory=torch.cuda.is_available(),
            persistent_workers=self.num_workers > 0,
            prefetch_factor=2 if self.num_workers > 0 else 2,
        )
```

## 🔍 数据质量检查

### 图像质量检查

```python
# scripts/check_data_quality.py
import cv2
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt

def check_image_quality(image_path):
    """检查单张图像质量"""
    
    img = cv2.imread(str(image_path))
    if img is None:
        return {'error': 'Cannot read image'}
    
    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    
    # 基础统计
    stats = {
        'shape': img_rgb.shape,
        'mean': np.mean(img_rgb),
        'std': np.std(img_rgb),
        'min': np.min(img_rgb),
        'max': np.max(img_rgb),
    }
    
    # 亮度分析
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    stats['brightness'] = np.mean(gray)
    
    # 对比度分析
    stats['contrast'] = np.std(gray)
    
    # 清晰度分析（拉普拉斯方差）
    laplacian = cv2.Laplacian(gray, cv2.CV_64F)
    stats['sharpness'] = np.var(laplacian)
    
    # 噪声估计
    stats['noise_level'] = estimate_noise(gray)
    
    return stats

def estimate_noise(image):
    """估计图像噪声水平"""
    H, W = image.shape
    M = [[1, -2, 1],
         [-2, 4, -2],
         [1, -2, 1]]
    
    sigma = np.sum(np.sum(np.absolute(cv2.filter2D(image, -1, np.array(M)))))
    sigma = sigma * np.sqrt(0.5 * np.pi) / (6 * (W-2) * (H-2))
    
    return sigma

def batch_quality_check(data_dir):
    """批量检查数据质量"""
    
    data_dir = Path(data_dir)
    results = {}
    
    for split in ['Train', 'Test']:
        for light_type in ['Low', 'Normal']:
            path = data_dir / split / light_type
            if not path.exists():
                continue
            
            key = f"{split}_{light_type}"
            results[key] = []
            
            for img_path in path.glob('*.png'):
                quality = check_image_quality(img_path)
                quality['filename'] = img_path.name
                results[key].append(quality)
    
    return results

# 生成质量报告
def generate_quality_report(results):
    """生成数据质量报告"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    for i, (key, data) in enumerate(results.items()):
        if not data or 'error' in data[0]:
            continue
        
        ax = axes[i // 2, i % 2]
        
        # 提取亮度值
        brightness_values = [item['brightness'] for item in data]
        
        ax.hist(brightness_values, bins=30, alpha=0.7, label=key)
        ax.set_title(f'{key} 亮度分布')
        ax.set_xlabel('亮度值')
        ax.set_ylabel('频次')
        ax.legend()
    
    plt.tight_layout()
    plt.savefig('data_quality_report.png', dpi=150)
    plt.show()

# 使用示例
if __name__ == "__main__":
    results = batch_quality_check('data/LOLv2/Real_captured')
    generate_quality_report(results)
```

### 配对验证

```python
def validate_image_pairs(data_root):
    """验证图像配对的质量"""
    
    data_root = Path(data_root)
    issues = []
    
    for split in ['Train', 'Test']:
        low_dir = data_root / split / 'Low'
        normal_dir = data_root / split / 'Normal'
        
        for low_path in low_dir.glob('*.png'):
            normal_path = normal_dir / low_path.name
            
            if not normal_path.exists():
                issues.append(f"缺少配对: {low_path}")
                continue
            
            # 检查图像尺寸是否匹配
            low_img = cv2.imread(str(low_path))
            normal_img = cv2.imread(str(normal_path))
            
            if low_img.shape != normal_img.shape:
                issues.append(f"尺寸不匹配: {low_path.name}")
            
            # 检查是否为有效的低光/正常光对
            low_brightness = np.mean(cv2.cvtColor(low_img, cv2.COLOR_BGR2GRAY))
            normal_brightness = np.mean(cv2.cvtColor(normal_img, cv2.COLOR_BGR2GRAY))
            
            if low_brightness >= normal_brightness:
                issues.append(f"亮度异常: {low_path.name} (低光={low_brightness:.1f}, 正常={normal_brightness:.1f})")
    
    return issues
```

## ❗ 常见问题和解决方案

### 数据加载问题

**问题1: 数据加载速度慢**
```python
# 解决方案：优化数据加载器设置
dataloader = DataLoader(
    dataset,
    batch_size=batch_size,
    num_workers=8,  # 增加worker数量
    pin_memory=True,  # 启用内存锁定
    persistent_workers=True,  # 保持worker进程
    prefetch_factor=4,  # 增加预取因子
)
```

**问题2: 内存不足**
```python
# 解决方案：减少批次大小和worker数量
dataloader = DataLoader(
    dataset,
    batch_size=4,  # 减少批次大小
    num_workers=2,  # 减少worker数量
    pin_memory=False,  # 禁用内存锁定
)
```

**问题3: 图像配对错误**
```bash
# 解决方案：运行验证脚本
python scripts/validate_dataset.py data/LOLv2/Real_captured
```

### 数据增强问题

**问题4: 增强后图像质量下降**
```python
# 解决方案：调整增强参数
transforms = A.Compose([
    A.RandomHorizontalFlip(p=0.5),
    A.RandomRotation(limit=5, p=0.3),  # 减少旋转角度
    A.ColorJitter(brightness=0.05, contrast=0.05, p=0.2),  # 减少颜色变化
])
```

**问题5: 训练不稳定**
```python
# 解决方案：使用更保守的增强策略
# 对于低光图像，过度增强可能破坏光照特性
conservative_transforms = A.Compose([
    A.RandomHorizontalFlip(p=0.5),  # 只使用水平翻转
    ToTensorV2(),
    A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])
```

### 性能优化建议

1. **使用SSD存储数据集**
2. **合理设置num_workers（通常为CPU核心数）**
3. **启用pin_memory（如果有GPU）**
4. **考虑数据缓存（如果内存充足）**
5. **使用适当的图像格式（PNG vs JPG）**

## 📚 进阶主题

### 自定义数据集

如果需要使用自己的数据集，请参考：
- [创建自定义数据集类](custom_dataset_creation.md)
- [数据集格式转换工具](dataset_conversion_tools.md)

### 数据增强研究

深入了解数据增强对低光图像增强的影响：
- [数据增强策略研究](data_augmentation_research.md)
- [领域自适应技术](domain_adaptation.md)

---

本指南涵盖了LLIE项目中数据处理的各个方面。如有问题，请参考[常见问题解答](faq.md)或提交Issue。

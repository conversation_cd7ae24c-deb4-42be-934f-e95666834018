# LLIE项目配置指南

本文档详细介绍了LLIE（低光图像增强）项目中各个配置文件的使用方法和参数说明。

## 目录结构

```
configs/
├── config.yaml              # 主配置文件
├── model/
│   └── dmfourllie.yaml      # LLIE模型配置
├── dataset/
│   └── LOLv2_Real.yaml      # LOL数据集配置
├── trainer/
│   └── default_trainer.yaml # 训练器配置
├── task/
│   ├── train.yaml           # 训练任务配置
│   ├── evaluate.yaml        # 评估任务配置
│   └── inference.yaml       # 推理任务配置
├── optimizer/
│   └── adam.yaml            # Adam优化器配置
└── scheduler/
    └── cosine.yaml          # 余弦退火调度器配置
```

## 快速开始

### 1. 基本训练
```bash
# 使用默认配置进行训练
python run.py

# 修改特定参数
python run.py trainer.batch_size=8 trainer.max_epochs=50
```

### 2. 模型评估
```bash
# 评估训练好的模型
python run.py task=evaluate evaluation.checkpoint_path=path/to/model.pth
```

### 3. 图像推理
```bash
# 对单张图像进行推理
python run.py task=inference \
  inference.checkpoint_path=path/to/model.pth \
  inference.input_path=path/to/image.jpg
```

## 配置文件详解

### 主配置文件 (config.yaml)

主配置文件是整个项目的入口点，它组合了其他所有配置组件。

#### 关键参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `name` | str | dmfourllie_experiment | 实验名称，用于输出目录和日志 |
| `seed` | int | 42 | 随机种子，确保实验可复现 |
| `device` | str | cuda | 计算设备，可选：cuda/cpu |
| `num_workers` | int | 4 | 数据加载进程数 |
| `use_validation` | bool | true | 是否在训练中进行验证 |

#### 使用示例

```bash
# 修改实验名称和设备
python run.py name=my_experiment device=cpu

# 设置数据加载进程数
python run.py num_workers=8
```

### 模型配置 (model/dmfourllie.yaml)

定义LLIE模型的架构参数。

#### 架构参数调优指南

| 参数 | 推荐范围 | 影响 | 调优建议 |
|------|----------|------|----------|
| `y_nf` | 16-32 | 亮度特征提取能力 | 增大提升质量但增加计算量 |
| `f_nf` | 16-32 | 频域处理能力 | 与y_nf保持相近 |
| `s_nf` | 32-64 | 空间特征融合 | 通常是y_nf的2倍 |
| `num_blocks` | 4-8 | 模型表达能力 | 更多块提升效果但增加训练时间 |

#### 使用示例

```bash
# 调整模型大小
python run.py model.architecture.s_nf=64 model.architecture.num_blocks=8

# 使用更小的模型（快速实验）
python run.py model.architecture.y_nf=8 model.architecture.f_nf=8 model.architecture.s_nf=16
```

### 训练器配置 (trainer/default_trainer.yaml)

控制训练过程的所有参数。

#### 重要参数详解

**学习率相关：**
- `optimizer.lr`: 初始学习率，通常从0.001开始调优
- `scheduler.T_max`: 调度周期，建议等于总训练轮数
- `scheduler.eta_min`: 最小学习率，通常是初始学习率的1/100

**训练稳定性：**
- `gradient_clip_norm`: 梯度裁剪，防止梯度爆炸
- `use_ema`: 指数移动平均，提高模型稳定性
- `use_amp`: 混合精度训练，加速训练并节省显存

#### 调优建议

1. **学习率过大症状**：损失震荡、训练不稳定
   ```bash
   python run.py trainer.optimizer.lr=0.0005
   ```

2. **显存不足**：减小批次大小，启用混合精度
   ```bash
   python run.py trainer.batch_size=8 trainer.use_amp=true
   ```

3. **训练过慢**：增大学习率，减少验证频率
   ```bash
   python run.py trainer.optimizer.lr=0.002 trainer.val_freq=5
   ```

### 数据集配置 (dataset/LOLv2_Real.yaml)

定义数据加载和预处理方式。

#### 数据增强策略

| 变换 | 目的 | 建议参数 |
|------|------|----------|
| RandomHorizontalFlip | 增加数据多样性 | p=0.5 |
| RandomRotation | 提升旋转鲁棒性 | degrees=10, p=0.3 |
| ColorJitter | 增强光照适应性 | brightness=0.1, p=0.3 |

#### 自定义数据集

如果使用自己的数据集，需要修改以下路径：

```yaml
train:
  low_light_dir: "path/to/your/train/low"
  normal_light_dir: "path/to/your/train/normal"

eval:
  low_light_dir: "path/to/your/test/low"
  normal_light_dir: "path/to/your/test/normal"
```

## 常见问题和解决方案

### 1. 训练不收敛
- 检查学习率是否过大
- 确认数据路径是否正确
- 验证数据预处理是否合适

### 2. 显存溢出
- 减小batch_size
- 启用混合精度训练(use_amp=true)
- 减小模型尺寸

### 3. 训练速度慢
- 增加num_workers
- 启用数据缓存(cache_images=true)
- 使用更大的batch_size

### 4. 验证指标不提升
- 检查早停设置
- 调整学习率调度策略
- 增加数据增强

## 高级用法

### 超参数扫描

使用Hydra的multirun功能进行超参数搜索：

```bash
# 搜索最佳学习率
python run.py --multirun trainer.optimizer.lr=0.0001,0.0005,0.001,0.002

# 搜索最佳模型大小
python run.py --multirun model.architecture.s_nf=16,32,64 trainer.batch_size=8,16,32
```

### 配置组合

创建自定义配置组合：

```bash
# 快速实验配置
python run.py trainer.max_epochs=10 trainer.val_freq=2 model.architecture.num_blocks=4

# 高质量训练配置
python run.py trainer.max_epochs=200 trainer.use_ema=true model.architecture.num_blocks=8
```

## 配置文件模板

### 创建新的数据集配置

如果需要使用新的数据集，可以复制`LOLv2_Real.yaml`并修改：

```yaml
# 新数据集配置模板
type: LOLDataset
name: "YourDataset"

train:
  low_light_dir: "data/YourDataset/train/low"
  normal_light_dir: "data/YourDataset/train/normal"
  transforms:
    - type: "RandomHorizontalFlip"
      p: 0.5
    - type: "ToTensor"
    - type: "Normalize"
      mean: [0.485, 0.456, 0.406]
      std: [0.229, 0.224, 0.225]

eval:
  low_light_dir: "data/YourDataset/test/low"
  normal_light_dir: "data/YourDataset/test/normal"
  transforms:
    - type: "ToTensor"
    - type: "Normalize"
      mean: [0.485, 0.456, 0.406]
      std: [0.229, 0.224, 0.225]
```

### 创建新的训练配置

针对不同的实验需求，可以创建专门的训练配置：

```yaml
# 快速实验配置 (configs/trainer/quick_experiment.yaml)
max_epochs: 20
batch_size: 8
val_batch_size: 1

loss:
  type: CharbonnierLoss
  reduction: mean
  eps: 1e-6

optimizer:
  type: Adam
  lr: 0.002
  weight_decay: 0.0001

scheduler:
  type: CosineAnnealingLR
  T_max: 20
  eta_min: 1e-6

use_ema: false
use_amp: true
val_freq: 2
log_freq: 5
```

## 环境变量配置

可以通过环境变量来覆盖配置：

```bash
# 设置CUDA设备
export CUDA_VISIBLE_DEVICES=0,1

# 设置数据根目录
export LLIE_DATA_ROOT=/path/to/your/data

# 设置输出目录
export LLIE_OUTPUT_DIR=/path/to/outputs
```

在配置文件中使用环境变量：

```yaml
# 在配置文件中引用环境变量
data_root: ${oc.env:LLIE_DATA_ROOT,data}
output_dir: ${oc.env:LLIE_OUTPUT_DIR,./outputs}
```

# 模型评估方法论

本文档详细介绍LLIE项目中模型评估的完整方法论，包括评估指标、评估流程、结果分析和性能对比等内容。

## 📋 目录

1. [评估指标体系](#评估指标体系)
2. [评估数据集准备](#评估数据集准备)
3. [评估流程设计](#评估流程设计)
4. [结果分析方法](#结果分析方法)
5. [性能对比基准](#性能对比基准)
6. [统计显著性检验](#统计显著性检验)
7. [用户研究评估](#用户研究评估)
8. [评估报告生成](#评估报告生成)

## 📊 评估指标体系

### 客观评估指标

#### 1. 像素级指标

**PSNR (Peak Signal-to-Noise Ratio)**
```python
import torch
import torch.nn.functional as F

def calculate_psnr(pred, target, max_val=1.0):
    """
    计算PSNR
    
    Args:
        pred: 预测图像 [B, C, H, W]
        target: 目标图像 [B, C, H, W]
        max_val: 像素最大值
    
    Returns:
        PSNR值 (dB)
    """
    mse = F.mse_loss(pred, target)
    if mse == 0:
        return float('inf')
    
    psnr = 20 * torch.log10(max_val / torch.sqrt(mse))
    return psnr.item()

# 使用示例
psnr_value = calculate_psnr(enhanced_images, reference_images)
print(f"PSNR: {psnr_value:.2f} dB")
```

**SSIM (Structural Similarity Index)**
```python
import torch
import torch.nn.functional as F

def calculate_ssim(pred, target, window_size=11, sigma=1.5):
    """
    计算SSIM
    
    Args:
        pred: 预测图像 [B, C, H, W]
        target: 目标图像 [B, C, H, W]
        window_size: 窗口大小
        sigma: 高斯核标准差
    
    Returns:
        SSIM值 [0, 1]
    """
    # 创建高斯窗口
    def create_window(window_size, sigma, channel):
        coords = torch.arange(window_size, dtype=torch.float32)
        coords -= window_size // 2
        g = torch.exp(-(coords ** 2) / (2 * sigma ** 2))
        g /= g.sum()
        
        window = g.unsqueeze(1) * g.unsqueeze(0)
        window = window.unsqueeze(0).unsqueeze(0)
        window = window.expand(channel, 1, window_size, window_size)
        return window
    
    channel = pred.size(1)
    window = create_window(window_size, sigma, channel)
    
    if pred.is_cuda:
        window = window.cuda(pred.get_device())
    
    # 计算均值
    mu1 = F.conv2d(pred, window, padding=window_size//2, groups=channel)
    mu2 = F.conv2d(target, window, padding=window_size//2, groups=channel)
    
    mu1_sq = mu1.pow(2)
    mu2_sq = mu2.pow(2)
    mu1_mu2 = mu1 * mu2
    
    # 计算方差和协方差
    sigma1_sq = F.conv2d(pred * pred, window, padding=window_size//2, groups=channel) - mu1_sq
    sigma2_sq = F.conv2d(target * target, window, padding=window_size//2, groups=channel) - mu2_sq
    sigma12 = F.conv2d(pred * target, window, padding=window_size//2, groups=channel) - mu1_mu2
    
    # SSIM常数
    C1 = 0.01 ** 2
    C2 = 0.03 ** 2
    
    # 计算SSIM
    ssim_map = ((2 * mu1_mu2 + C1) * (2 * sigma12 + C2)) / \
               ((mu1_sq + mu2_sq + C1) * (sigma1_sq + sigma2_sq + C2))
    
    return ssim_map.mean().item()

# 使用示例
ssim_value = calculate_ssim(enhanced_images, reference_images)
print(f"SSIM: {ssim_value:.4f}")
```

**MAE (Mean Absolute Error)**
```python
def calculate_mae(pred, target):
    """计算平均绝对误差"""
    mae = F.l1_loss(pred, target)
    return mae.item()

# 使用示例
mae_value = calculate_mae(enhanced_images, reference_images)
print(f"MAE: {mae_value:.4f}")
```

#### 2. 感知质量指标

**LPIPS (Learned Perceptual Image Patch Similarity)**
```python
import lpips

def calculate_lpips(pred, target, net='alex'):
    """
    计算LPIPS感知损失
    
    Args:
        pred: 预测图像 [B, C, H, W], 范围[-1, 1]
        target: 目标图像 [B, C, H, W], 范围[-1, 1]
        net: 网络类型 ('alex', 'vgg', 'squeeze')
    
    Returns:
        LPIPS值 (越小越好)
    """
    loss_fn = lpips.LPIPS(net=net)
    
    if pred.is_cuda:
        loss_fn = loss_fn.cuda()
    
    # 确保输入范围在[-1, 1]
    pred_norm = pred * 2.0 - 1.0
    target_norm = target * 2.0 - 1.0
    
    lpips_value = loss_fn(pred_norm, target_norm)
    return lpips_value.mean().item()

# 使用示例
lpips_value = calculate_lpips(enhanced_images, reference_images)
print(f"LPIPS: {lpips_value:.4f}")
```

#### 3. 低光增强专用指标

**亮度改善比 (Brightness Enhancement Ratio)**
```python
def calculate_brightness_enhancement_ratio(low_img, enhanced_img):
    """计算亮度改善比"""
    low_brightness = torch.mean(low_img)
    enhanced_brightness = torch.mean(enhanced_img)
    
    ratio = enhanced_brightness / (low_brightness + 1e-8)
    return ratio.item()

# 使用示例
ber = calculate_brightness_enhancement_ratio(low_images, enhanced_images)
print(f"亮度改善比: {ber:.2f}")
```

**对比度改善指标 (Contrast Enhancement)**
```python
def calculate_contrast_enhancement(low_img, enhanced_img):
    """计算对比度改善"""
    def image_contrast(img):
        # 使用标准差作为对比度度量
        return torch.std(img)
    
    low_contrast = image_contrast(low_img)
    enhanced_contrast = image_contrast(enhanced_img)
    
    improvement = enhanced_contrast / (low_contrast + 1e-8)
    return improvement.item()

# 使用示例
contrast_improvement = calculate_contrast_enhancement(low_images, enhanced_images)
print(f"对比度改善: {contrast_improvement:.2f}")
```

**信息熵增益 (Information Entropy Gain)**
```python
def calculate_entropy_gain(low_img, enhanced_img):
    """计算信息熵增益"""
    def image_entropy(img):
        # 转换为灰度图并计算直方图
        gray = torch.mean(img, dim=1, keepdim=True)
        hist = torch.histc(gray, bins=256, min=0, max=1)
        hist = hist / hist.sum()
        
        # 计算熵
        entropy = -torch.sum(hist * torch.log2(hist + 1e-8))
        return entropy
    
    low_entropy = image_entropy(low_img)
    enhanced_entropy = image_entropy(enhanced_img)
    
    gain = enhanced_entropy - low_entropy
    return gain.item()

# 使用示例
entropy_gain = calculate_entropy_gain(low_images, enhanced_images)
print(f"信息熵增益: {entropy_gain:.2f}")
```

### 综合评估指标

```python
class ComprehensiveEvaluator:
    """综合评估器"""
    
    def __init__(self):
        self.lpips_fn = lpips.LPIPS(net='alex')
        if torch.cuda.is_available():
            self.lpips_fn = self.lpips_fn.cuda()
    
    def evaluate_batch(self, low_images, enhanced_images, reference_images):
        """批量评估"""
        results = {
            # 像素级指标
            'psnr': calculate_psnr(enhanced_images, reference_images),
            'ssim': calculate_ssim(enhanced_images, reference_images),
            'mae': calculate_mae(enhanced_images, reference_images),
            
            # 感知质量指标
            'lpips': calculate_lpips(enhanced_images, reference_images),
            
            # 低光增强专用指标
            'brightness_ratio': calculate_brightness_enhancement_ratio(low_images, enhanced_images),
            'contrast_improvement': calculate_contrast_enhancement(low_images, enhanced_images),
            'entropy_gain': calculate_entropy_gain(low_images, enhanced_images),
        }
        
        return results
    
    def evaluate_dataset(self, dataloader, model):
        """评估整个数据集"""
        model.eval()
        all_results = []
        
        with torch.no_grad():
            for batch in tqdm(dataloader, desc="评估中"):
                low_images = batch['low']
                reference_images = batch['normal']
                
                # 模型推理
                enhanced_images = model(low_images)
                
                # 计算指标
                batch_results = self.evaluate_batch(
                    low_images, enhanced_images, reference_images
                )
                all_results.append(batch_results)
        
        # 汇总结果
        final_results = {}
        for key in all_results[0].keys():
            values = [result[key] for result in all_results]
            final_results[key] = {
                'mean': np.mean(values),
                'std': np.std(values),
                'min': np.min(values),
                'max': np.max(values)
            }
        
        return final_results

# 使用示例
evaluator = ComprehensiveEvaluator()
results = evaluator.evaluate_dataset(test_dataloader, model)

for metric, stats in results.items():
    print(f"{metric}: {stats['mean']:.4f} ± {stats['std']:.4f}")
```

## 📁 评估数据集准备

### 标准测试集

```python
# 评估数据集配置
EVALUATION_DATASETS = {
    'LOLv1': {
        'test_path': 'data/LOLv1/Test',
        'num_images': 15,
        'description': 'LOL原始测试集'
    },
    'LOLv2_Real': {
        'test_path': 'data/LOLv2/Real_captured/Test',
        'num_images': 100,
        'description': 'LOLv2真实拍摄测试集'
    },
    'LOLv2_Synthetic': {
        'test_path': 'data/LOLv2/Synthetic/Test',
        'num_images': 100,
        'description': 'LOLv2合成测试集'
    },
    'DICM': {
        'test_path': 'data/DICM',
        'num_images': 44,
        'description': 'DICM数据集'
    },
    'LIME': {
        'test_path': 'data/LIME',
        'num_images': 10,
        'description': 'LIME数据集'
    }
}
```

### 自定义评估集创建

```python
def create_evaluation_subset(source_dir, output_dir, subset_size=50, selection_strategy='random'):
    """
    创建评估子集
    
    Args:
        source_dir: 源数据目录
        output_dir: 输出目录
        subset_size: 子集大小
        selection_strategy: 选择策略 ('random', 'diverse', 'challenging')
    """
    source_dir = Path(source_dir)
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取所有图像
    image_paths = list(source_dir.glob('*.png')) + list(source_dir.glob('*.jpg'))
    
    if selection_strategy == 'random':
        # 随机选择
        selected_paths = np.random.choice(image_paths, subset_size, replace=False)
        
    elif selection_strategy == 'diverse':
        # 基于图像特征的多样性选择
        selected_paths = select_diverse_images(image_paths, subset_size)
        
    elif selection_strategy == 'challenging':
        # 选择具有挑战性的图像
        selected_paths = select_challenging_images(image_paths, subset_size)
    
    # 复制选中的图像
    for i, path in enumerate(selected_paths):
        shutil.copy2(path, output_dir / f"{i:03d}{path.suffix}")
    
    print(f"✅ 创建评估子集完成: {len(selected_paths)} 张图像")

def select_diverse_images(image_paths, subset_size):
    """基于图像特征选择多样化的图像"""
    features = []
    
    for img_path in image_paths:
        img = Image.open(img_path).convert('RGB')
        img_array = np.array(img.resize((64, 64))) / 255.0
        
        # 提取简单特征
        feature = {
            'brightness': np.mean(img_array),
            'contrast': np.std(img_array),
            'color_variance': np.var(img_array, axis=(0, 1)).mean(),
        }
        features.append(feature)
    
    # 使用K-means聚类选择代表性图像
    from sklearn.cluster import KMeans
    
    feature_matrix = np.array([[f['brightness'], f['contrast'], f['color_variance']] 
                              for f in features])
    
    kmeans = KMeans(n_clusters=subset_size, random_state=42)
    clusters = kmeans.fit_predict(feature_matrix)
    
    # 从每个聚类中选择最接近中心的图像
    selected_indices = []
    for i in range(subset_size):
        cluster_indices = np.where(clusters == i)[0]
        if len(cluster_indices) > 0:
            center = kmeans.cluster_centers_[i]
            distances = np.linalg.norm(feature_matrix[cluster_indices] - center, axis=1)
            closest_idx = cluster_indices[np.argmin(distances)]
            selected_indices.append(closest_idx)
    
    return [image_paths[i] for i in selected_indices]
```

## 🔄 评估流程设计

### 标准评估流程

```python
class StandardEvaluationPipeline:
    """标准评估流程"""
    
    def __init__(self, model, config):
        self.model = model
        self.config = config
        self.evaluator = ComprehensiveEvaluator()
        self.results = {}
    
    def run_evaluation(self, test_datasets):
        """运行完整评估"""
        print("🚀 开始模型评估...")
        
        for dataset_name, dataset_config in test_datasets.items():
            print(f"\n📊 评估数据集: {dataset_name}")
            
            # 创建数据加载器
            dataloader = self.create_dataloader(dataset_config)
            
            # 评估模型
            dataset_results = self.evaluator.evaluate_dataset(dataloader, self.model)
            self.results[dataset_name] = dataset_results
            
            # 打印结果
            self.print_dataset_results(dataset_name, dataset_results)
        
        # 生成综合报告
        self.generate_summary_report()
        
        return self.results
    
    def create_dataloader(self, dataset_config):
        """创建数据加载器"""
        # 实现数据加载器创建逻辑
        pass
    
    def print_dataset_results(self, dataset_name, results):
        """打印数据集结果"""
        print(f"\n{dataset_name} 评估结果:")
        print("-" * 50)
        
        for metric, stats in results.items():
            print(f"{metric:15s}: {stats['mean']:8.4f} ± {stats['std']:6.4f}")
    
    def generate_summary_report(self):
        """生成综合报告"""
        # 计算平均性能
        all_metrics = {}
        for dataset_results in self.results.values():
            for metric, stats in dataset_results.items():
                if metric not in all_metrics:
                    all_metrics[metric] = []
                all_metrics[metric].append(stats['mean'])
        
        print(f"\n📈 综合评估结果:")
        print("=" * 60)
        
        for metric, values in all_metrics.items():
            avg_value = np.mean(values)
            std_value = np.std(values)
            print(f"{metric:15s}: {avg_value:8.4f} ± {std_value:6.4f}")

# 使用示例
pipeline = StandardEvaluationPipeline(model, config)
results = pipeline.run_evaluation(EVALUATION_DATASETS)
```

### 交叉验证评估

```python
def cross_validation_evaluation(model_class, dataset, k_folds=5):
    """K折交叉验证评估"""
    from sklearn.model_selection import KFold
    
    # 准备数据索引
    n_samples = len(dataset)
    indices = np.arange(n_samples)
    
    kfold = KFold(n_splits=k_folds, shuffle=True, random_state=42)
    
    fold_results = []
    
    for fold, (train_idx, val_idx) in enumerate(kfold.split(indices)):
        print(f"\n🔄 第 {fold + 1}/{k_folds} 折验证")
        
        # 创建训练和验证子集
        train_subset = torch.utils.data.Subset(dataset, train_idx)
        val_subset = torch.utils.data.Subset(dataset, val_idx)
        
        # 训练模型
        model = model_class()
        train_model(model, train_subset)
        
        # 评估模型
        evaluator = ComprehensiveEvaluator()
        val_dataloader = DataLoader(val_subset, batch_size=16, shuffle=False)
        fold_result = evaluator.evaluate_dataset(val_dataloader, model)
        
        fold_results.append(fold_result)
        
        # 打印折结果
        print(f"第 {fold + 1} 折结果:")
        for metric, stats in fold_result.items():
            print(f"  {metric}: {stats['mean']:.4f}")
    
    # 汇总交叉验证结果
    cv_results = {}
    for metric in fold_results[0].keys():
        values = [fold[metric]['mean'] for fold in fold_results]
        cv_results[metric] = {
            'mean': np.mean(values),
            'std': np.std(values),
            'folds': values
        }
    
    print(f"\n📊 {k_folds}折交叉验证结果:")
    print("=" * 50)
    for metric, stats in cv_results.items():
        print(f"{metric:15s}: {stats['mean']:8.4f} ± {stats['std']:6.4f}")
    
    return cv_results
```

## 📈 结果分析方法

### 性能分析可视化

```python
import matplotlib.pyplot as plt
import seaborn as sns

def create_performance_radar_chart(results, save_path=None):
    """创建性能雷达图"""
    
    # 准备数据
    metrics = ['PSNR', 'SSIM', 'LPIPS_inv', 'Brightness', 'Contrast']
    values = [
        results['psnr']['mean'] / 30,  # 归一化到[0,1]
        results['ssim']['mean'],
        1 - results['lpips']['mean'],  # LPIPS越小越好，所以取反
        min(results['brightness_ratio']['mean'] / 3, 1),  # 限制在[0,1]
        min(results['contrast_improvement']['mean'] / 3, 1)
    ]
    
    # 创建雷达图
    angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False)
    values += values[:1]  # 闭合图形
    angles = np.concatenate((angles, [angles[0]]))
    
    fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(projection='polar'))
    ax.plot(angles, values, 'o-', linewidth=2, label='模型性能')
    ax.fill(angles, values, alpha=0.25)
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metrics)
    ax.set_ylim(0, 1)
    ax.set_title('模型性能雷达图', size=16, fontweight='bold', pad=20)
    ax.grid(True)
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
    
    plt.show()

def create_metric_distribution_plot(results, save_path=None):
    """创建指标分布图"""
    
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    axes = axes.flatten()
    
    metrics = ['psnr', 'ssim', 'mae', 'lpips', 'brightness_ratio', 'contrast_improvement']
    metric_names = ['PSNR (dB)', 'SSIM', 'MAE', 'LPIPS', '亮度比', '对比度改善']
    
    for i, (metric, name) in enumerate(zip(metrics, metric_names)):
        if metric in results:
            # 这里假设我们有每张图像的详细结果
            # 实际使用时需要修改为真实的分布数据
            mean_val = results[metric]['mean']
            std_val = results[metric]['std']
            
            # 生成模拟分布数据用于演示
            data = np.random.normal(mean_val, std_val, 100)
            
            axes[i].hist(data, bins=20, alpha=0.7, edgecolor='black')
            axes[i].axvline(mean_val, color='red', linestyle='--', 
                           label=f'均值: {mean_val:.3f}')
            axes[i].set_title(name)
            axes[i].set_xlabel('值')
            axes[i].set_ylabel('频次')
            axes[i].legend()
            axes[i].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
    
    plt.show()

def analyze_failure_cases(model, test_dataloader, threshold_psnr=15):
    """分析失败案例"""
    
    model.eval()
    failure_cases = []
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(test_dataloader):
            low_images = batch['low']
            reference_images = batch['normal']
            image_paths = batch.get('path', [f'batch_{batch_idx}_img_{i}' for i in range(len(low_images))])
            
            # 模型推理
            enhanced_images = model(low_images)
            
            # 计算每张图像的PSNR
            for i in range(len(low_images)):
                psnr = calculate_psnr(
                    enhanced_images[i:i+1], 
                    reference_images[i:i+1]
                )
                
                if psnr < threshold_psnr:
                    failure_cases.append({
                        'path': image_paths[i],
                        'psnr': psnr,
                        'low_image': low_images[i],
                        'enhanced_image': enhanced_images[i],
                        'reference_image': reference_images[i]
                    })
    
    # 分析失败原因
    print(f"🔍 发现 {len(failure_cases)} 个失败案例 (PSNR < {threshold_psnr})")
    
    if failure_cases:
        # 按PSNR排序
        failure_cases.sort(key=lambda x: x['psnr'])
        
        # 显示最差的几个案例
        for i, case in enumerate(failure_cases[:5]):
            print(f"案例 {i+1}: {case['path']}, PSNR: {case['psnr']:.2f}")
        
        # 可视化失败案例
        visualize_failure_cases(failure_cases[:9])
    
    return failure_cases

def visualize_failure_cases(failure_cases):
    """可视化失败案例"""
    
    n_cases = min(len(failure_cases), 9)
    fig, axes = plt.subplots(n_cases, 3, figsize=(12, 4 * n_cases))
    
    if n_cases == 1:
        axes = axes.reshape(1, -1)
    
    for i, case in enumerate(failure_cases[:n_cases]):
        # 转换图像格式用于显示
        low_img = case['low_image'].cpu().numpy().transpose(1, 2, 0)
        enhanced_img = case['enhanced_image'].cpu().numpy().transpose(1, 2, 0)
        ref_img = case['reference_image'].cpu().numpy().transpose(1, 2, 0)
        
        # 确保像素值在[0,1]范围内
        low_img = np.clip(low_img, 0, 1)
        enhanced_img = np.clip(enhanced_img, 0, 1)
        ref_img = np.clip(ref_img, 0, 1)
        
        axes[i, 0].imshow(low_img)
        axes[i, 0].set_title(f'低光图像')
        axes[i, 0].axis('off')
        
        axes[i, 1].imshow(enhanced_img)
        axes[i, 1].set_title(f'增强结果\nPSNR: {case["psnr"]:.2f}')
        axes[i, 1].axis('off')
        
        axes[i, 2].imshow(ref_img)
        axes[i, 2].set_title(f'参考图像')
        axes[i, 2].axis('off')
    
    plt.tight_layout()
    plt.savefig('failure_cases_analysis.png', dpi=150, bbox_inches='tight')
    plt.show()
```

## 🏆 性能对比基准

### 基准模型对比

```python
# 基准模型配置
BASELINE_MODELS = {
    'RetinexNet': {
        'paper': 'Deep Retinex Decomposition for Low-Light Enhancement',
        'year': 2018,
        'reported_psnr': 16.77,  # 在LOL数据集上的报告结果
        'reported_ssim': 0.56
    },
    'KinD': {
        'paper': 'Kindling the Darkness: A Practical Low-light Image Enhancer',
        'year': 2019,
        'reported_psnr': 20.87,
        'reported_ssim': 0.82
    },
    'EnlightenGAN': {
        'paper': 'EnlightenGAN: Deep Light Enhancement without Paired Supervision',
        'year': 2019,
        'reported_psnr': 17.48,
        'reported_ssim': 0.65
    },
    'RUAS': {
        'paper': 'RUAS: Robust Unsupervised Attention-guided Semantic Segmentation',
        'year': 2020,
        'reported_psnr': 18.23,
        'reported_ssim': 0.72
    }
}

def compare_with_baselines(our_results, dataset_name='LOLv1'):
    """与基准模型对比"""
    
    print(f"\n📊 在 {dataset_name} 数据集上的性能对比:")
    print("=" * 70)
    print(f"{'模型':<15} {'年份':<6} {'PSNR':<8} {'SSIM':<8} {'来源'}")
    print("-" * 70)
    
    # 显示基准模型结果
    for model_name, info in BASELINE_MODELS.items():
        print(f"{model_name:<15} {info['year']:<6} {info['reported_psnr']:<8.2f} "
              f"{info['reported_ssim']:<8.3f} 论文报告")
    
    # 显示我们的结果
    our_psnr = our_results['psnr']['mean']
    our_ssim = our_results['ssim']['mean']
    print(f"{'LLIE':<15} {'2024':<6} {our_psnr:<8.2f} "
          f"{our_ssim:<8.3f} 本次评估")
    
    print("-" * 70)
    
    # 计算改进
    best_baseline_psnr = max(info['reported_psnr'] for info in BASELINE_MODELS.values())
    best_baseline_ssim = max(info['reported_ssim'] for info in BASELINE_MODELS.values())
    
    psnr_improvement = our_psnr - best_baseline_psnr
    ssim_improvement = our_ssim - best_baseline_ssim
    
    print(f"\n📈 相对最佳基准的改进:")
    print(f"PSNR: {psnr_improvement:+.2f} dB")
    print(f"SSIM: {ssim_improvement:+.3f}")

def create_comparison_chart(our_results, save_path=None):
    """创建对比图表"""
    
    # 准备数据
    models = list(BASELINE_MODELS.keys()) + ['LLIE (Ours)']
    psnr_values = [info['reported_psnr'] for info in BASELINE_MODELS.values()] + [our_results['psnr']['mean']]
    ssim_values = [info['reported_ssim'] for info in BASELINE_MODELS.values()] + [our_results['ssim']['mean']]
    
    # 创建对比图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # PSNR对比
    bars1 = ax1.bar(models, psnr_values, color=['skyblue'] * len(BASELINE_MODELS) + ['orange'])
    ax1.set_title('PSNR 对比', fontsize=14, fontweight='bold')
    ax1.set_ylabel('PSNR (dB)')
    ax1.tick_params(axis='x', rotation=45)
    
    # 添加数值标签
    for bar, value in zip(bars1, psnr_values):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                f'{value:.2f}', ha='center', va='bottom')
    
    # SSIM对比
    bars2 = ax2.bar(models, ssim_values, color=['lightcoral'] * len(BASELINE_MODELS) + ['green'])
    ax2.set_title('SSIM 对比', fontsize=14, fontweight='bold')
    ax2.set_ylabel('SSIM')
    ax2.tick_params(axis='x', rotation=45)
    
    # 添加数值标签
    for bar, value in zip(bars2, ssim_values):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{value:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
    
    plt.show()
```

## 📊 统计显著性检验

### 配对t检验

```python
from scipy import stats

def statistical_significance_test(results_a, results_b, metric='psnr', alpha=0.05):
    """
    统计显著性检验
    
    Args:
        results_a: 模型A的结果列表
        results_b: 模型B的结果列表
        metric: 比较的指标
        alpha: 显著性水平
    
    Returns:
        检验结果字典
    """
    
    # 配对t检验
    t_stat, p_value = stats.ttest_rel(results_a, results_b)
    
    # 效应大小 (Cohen's d)
    mean_diff = np.mean(results_a) - np.mean(results_b)
    pooled_std = np.sqrt((np.var(results_a) + np.var(results_b)) / 2)
    cohens_d = mean_diff / pooled_std
    
    # 置信区间
    diff = np.array(results_a) - np.array(results_b)
    ci_lower, ci_upper = stats.t.interval(
        1 - alpha, len(diff) - 1, 
        loc=np.mean(diff), 
        scale=stats.sem(diff)
    )
    
    result = {
        'metric': metric,
        'mean_a': np.mean(results_a),
        'mean_b': np.mean(results_b),
        'mean_difference': mean_diff,
        't_statistic': t_stat,
        'p_value': p_value,
        'is_significant': p_value < alpha,
        'cohens_d': cohens_d,
        'confidence_interval': (ci_lower, ci_upper),
        'effect_size_interpretation': interpret_cohens_d(cohens_d)
    }
    
    return result

def interpret_cohens_d(d):
    """解释Cohen's d效应大小"""
    abs_d = abs(d)
    if abs_d < 0.2:
        return "微小效应"
    elif abs_d < 0.5:
        return "小效应"
    elif abs_d < 0.8:
        return "中等效应"
    else:
        return "大效应"

def print_significance_test_results(test_result):
    """打印显著性检验结果"""
    
    print(f"\n📊 {test_result['metric'].upper()} 统计显著性检验结果:")
    print("=" * 50)
    print(f"模型A均值: {test_result['mean_a']:.4f}")
    print(f"模型B均值: {test_result['mean_b']:.4f}")
    print(f"均值差异: {test_result['mean_difference']:.4f}")
    print(f"t统计量: {test_result['t_statistic']:.4f}")
    print(f"p值: {test_result['p_value']:.6f}")
    print(f"显著性: {'是' if test_result['is_significant'] else '否'}")
    print(f"Cohen's d: {test_result['cohens_d']:.4f} ({test_result['effect_size_interpretation']})")
    print(f"95%置信区间: [{test_result['confidence_interval'][0]:.4f}, {test_result['confidence_interval'][1]:.4f}]")

# 使用示例
# 假设我们有两个模型在多张图像上的PSNR结果
model_a_psnr = [22.5, 23.1, 21.8, 24.2, 22.9, 23.5, 22.1, 23.8, 22.7, 23.3]
model_b_psnr = [21.2, 22.1, 20.8, 23.1, 21.9, 22.5, 21.1, 22.8, 21.7, 22.3]

test_result = statistical_significance_test(model_a_psnr, model_b_psnr, 'psnr')
print_significance_test_results(test_result)
```

---

本文档提供了LLIE项目中模型评估的完整方法论。通过系统性的评估指标、科学的评估流程和深入的结果分析，您可以全面评估模型性能并与现有方法进行公平对比。

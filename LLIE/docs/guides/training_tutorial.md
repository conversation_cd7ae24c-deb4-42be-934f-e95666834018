# 模型训练详细教程

本教程详细介绍LLIE项目中模型训练的各个环节，包括配置设置、训练监控、调试技巧和超参数调优等内容。

## 📋 目录

1. [训练前准备](#训练前准备)
2. [配置文件详解](#配置文件详解)
3. [训练流程控制](#训练流程控制)
4. [监控和日志](#监控和日志)
5. [调试和故障排除](#调试和故障排除)
6. [超参数调优](#超参数调优)
7. [高级训练技巧](#高级训练技巧)
8. [分布式训练](#分布式训练)
9. [训练监控最佳实践](#训练监控最佳实践)

## 🚀 训练前准备

### 环境检查

```bash
# 1. 验证环境配置
python -c "
import torch
import torch.cuda
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA版本: {torch.version.cuda}')
print(f'CUDA可用: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'GPU数量: {torch.cuda.device_count()}')
    print(f'当前GPU: {torch.cuda.get_device_name()}')
    print(f'GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB')
"

# 2. 检查数据集
python scripts/validate_dataset.py data/LOLv2/Real_captured

# 3. 测试模型加载
python -c "
from src.llie.models import build_model
from omegaconf import OmegaConf

cfg = OmegaConf.load('configs/model/dmfourllie.yaml')
model = build_model(cfg)
print(f'模型参数量: {sum(p.numel() for p in model.parameters()) / 1e6:.2f}M')
"
```

### 目录结构准备

```bash
# 创建必要的输出目录
mkdir -p outputs/{checkpoints,logs,visualizations,experiments}
mkdir -p data/cache  # 数据缓存目录

# 检查目录权限
ls -la outputs/
```

### 配置文件检查

```python
# scripts/check_config.py
from omegaconf import OmegaConf
import sys

def validate_config(config_path):
    """验证配置文件"""
    
    try:
        cfg = OmegaConf.load(config_path)
        print(f"✅ 配置文件 {config_path} 加载成功")
        
        # 检查必要字段
        required_fields = [
            'model', 'dataset', 'trainer', 'optimizer', 'scheduler'
        ]
        
        missing_fields = []
        for field in required_fields:
            if field not in cfg:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ 缺少必要字段: {missing_fields}")
            return False
        
        # 检查路径
        if 'data_root' in cfg.dataset:
            import os
            if not os.path.exists(cfg.dataset.data_root):
                print(f"⚠️ 数据路径不存在: {cfg.dataset.data_root}")
        
        print("✅ 配置文件验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件错误: {e}")
        return False

if __name__ == "__main__":
    config_path = sys.argv[1] if len(sys.argv) > 1 else 'configs/config.yaml'
    validate_config(config_path)
```

## ⚙️ 配置文件详解

### 主配置文件结构

```yaml
# configs/config.yaml - 主配置文件
defaults:
  - model: dmfourllie          # 模型配置
  - dataset: LOLv2_Real        # 数据集配置
  - trainer: default           # 训练器配置
  - optimizer: adamw           # 优化器配置
  - scheduler: cosine          # 学习率调度器配置
  - _self_                     # 当前文件优先级最高

# 实验配置
name: "dmfourllie_experiment"  # 实验名称
seed: 42                       # 随机种子
debug: false                   # 调试模式

# 设备配置
device: "auto"                 # 自动选择设备
mixed_precision: true          # 混合精度训练

# 输出配置
output_dir: "outputs"          # 输出目录
save_top_k: 3                  # 保存最好的k个模型
save_last: true                # 保存最后一个模型

# Weights & Biases配置
wandb:
  project: "LLIE"              # 项目名称
  entity: null                 # 团队名称（可选）
  tags: ["dmfourllie", "lol"]  # 标签
  notes: "LLIE模型训练"   # 实验描述
  log_model: true              # 是否上传模型
```

### 训练器配置详解

```yaml
# configs/trainer/default.yaml
# 基础训练参数
max_epochs: 100                # 最大训练轮数
batch_size: 16                 # 批次大小
accumulate_grad_batches: 1     # 梯度累积步数
gradient_clip_val: 1.0         # 梯度裁剪阈值

# 验证配置
val_check_interval: 1.0        # 验证频率（每轮验证一次）
check_val_every_n_epoch: 1     # 每n轮验证一次
limit_val_batches: 1.0         # 验证集使用比例

# 早停配置
early_stopping:
  monitor: "val_psnr"          # 监控指标
  patience: 15                 # 耐心值
  mode: "max"                  # 最大化还是最小化
  min_delta: 0.001             # 最小改进阈值

# 模型检查点配置
checkpoint:
  monitor: "val_psnr"          # 监控指标
  mode: "max"                  # 保存最佳模型
  save_top_k: 3                # 保存前k个模型
  save_last: true              # 保存最后一个模型
  filename: "epoch_{epoch:02d}-psnr_{val_psnr:.2f}"

# 损失函数配置
loss:
  type: "CombinedLoss"         # 损失函数类型
  pixel_weight: 1.0            # 像素损失权重
  ssim_weight: 0.1             # SSIM损失权重
  perceptual_weight: 0.0       # 感知损失权重

# 数据加载配置
num_workers: 8                 # 数据加载进程数
pin_memory: true               # 内存锁定
persistent_workers: true       # 保持worker进程
```

### 优化器配置

```yaml
# configs/optimizer/adamw.yaml
type: "AdamW"                  # 优化器类型
lr: 0.0002                     # 学习率
weight_decay: 0.01             # 权重衰减
betas: [0.9, 0.999]           # Adam参数
eps: 1e-8                      # 数值稳定性参数

# 不同层的学习率
param_groups:
  - name: "backbone"           # 主干网络
    lr_mult: 1.0               # 学习率倍数
  - name: "head"               # 输出头
    lr_mult: 2.0               # 更高的学习率
```

### 学习率调度器配置

```yaml
# configs/scheduler/cosine.yaml
type: "CosineAnnealingLR"      # 余弦退火调度器
T_max: 100                     # 周期长度
eta_min: 1e-6                  # 最小学习率

# 预热配置
warmup:
  enabled: true                # 启用预热
  steps: 1000                  # 预热步数
  start_lr: 1e-6              # 起始学习率
```

## 🎯 训练流程控制

### 基础训练命令

```bash
# 1. 使用默认配置训练
python run.py

# 2. 指定实验名称
python run.py name=my_experiment

# 3. 修改训练参数
python run.py trainer.max_epochs=50 trainer.batch_size=8

# 4. 使用不同的模型配置
python run.py model=dmfourllie_light

# 5. 调试模式（快速验证）
python run.py debug=true trainer.max_epochs=2 trainer.limit_train_batches=10
```

### 高级训练选项

```bash
# 混合精度训练（节省显存）
python run.py trainer.mixed_precision=true

# 梯度累积（模拟更大的批次）
python run.py trainer.batch_size=8 trainer.accumulate_grad_batches=4

# 从检查点恢复训练
python run.py trainer.resume_from_checkpoint=outputs/checkpoints/last.ckpt

# 多GPU训练
python run.py trainer.devices=2 trainer.strategy=ddp

# 指定GPU
CUDA_VISIBLE_DEVICES=0,1 python run.py trainer.devices=2
```

### 训练脚本示例

```python
# scripts/train_model.py
import hydra
from omegaconf import DictConfig, OmegaConf
import torch
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping
from pytorch_lightning.loggers import WandbLogger

from src.llie.models import build_model
from src.llie.data import build_dataloader
from src.llie.training import LightningModule

@hydra.main(version_base=None, config_path="../configs", config_name="config")
def train(cfg: DictConfig) -> None:
    """训练主函数"""
    
    # 设置随机种子
    pl.seed_everything(cfg.seed, workers=True)
    
    # 创建模型
    model = build_model(cfg.model)
    
    # 创建数据加载器
    train_loader = build_dataloader(cfg.dataset, split='train', batch_size=cfg.trainer.batch_size)
    val_loader = build_dataloader(cfg.dataset, split='val', batch_size=cfg.trainer.batch_size)
    
    # 创建Lightning模块
    lightning_module = LightningModule(
        model=model,
        optimizer_cfg=cfg.optimizer,
        scheduler_cfg=cfg.scheduler,
        loss_cfg=cfg.trainer.loss
    )
    
    # 配置回调函数
    callbacks = []
    
    # 模型检查点
    checkpoint_callback = ModelCheckpoint(
        dirpath=f"{cfg.output_dir}/checkpoints",
        filename=cfg.trainer.checkpoint.filename,
        monitor=cfg.trainer.checkpoint.monitor,
        mode=cfg.trainer.checkpoint.mode,
        save_top_k=cfg.trainer.checkpoint.save_top_k,
        save_last=cfg.trainer.checkpoint.save_last,
    )
    callbacks.append(checkpoint_callback)
    
    # 早停
    if cfg.trainer.early_stopping.enabled:
        early_stop_callback = EarlyStopping(
            monitor=cfg.trainer.early_stopping.monitor,
            patience=cfg.trainer.early_stopping.patience,
            mode=cfg.trainer.early_stopping.mode,
            min_delta=cfg.trainer.early_stopping.min_delta,
        )
        callbacks.append(early_stop_callback)
    
    # 配置日志记录器
    logger = None
    if cfg.wandb.enabled:
        logger = WandbLogger(
            project=cfg.wandb.project,
            name=cfg.name,
            tags=cfg.wandb.tags,
            notes=cfg.wandb.notes,
        )
    
    # 创建训练器
    trainer = pl.Trainer(
        max_epochs=cfg.trainer.max_epochs,
        devices=cfg.trainer.devices,
        accelerator=cfg.trainer.accelerator,
        strategy=cfg.trainer.strategy,
        precision=16 if cfg.trainer.mixed_precision else 32,
        callbacks=callbacks,
        logger=logger,
        val_check_interval=cfg.trainer.val_check_interval,
        gradient_clip_val=cfg.trainer.gradient_clip_val,
        accumulate_grad_batches=cfg.trainer.accumulate_grad_batches,
    )
    
    # 开始训练
    trainer.fit(lightning_module, train_loader, val_loader)
    
    # 测试最佳模型
    trainer.test(lightning_module, val_loader, ckpt_path="best")

if __name__ == "__main__":
    train()
```

## 📊 监控和日志

### Weights & Biases集成

```python
# 在Lightning模块中集成W&B
class LightningModule(pl.LightningModule):
    
    def training_step(self, batch, batch_idx):
        # 训练步骤
        loss, metrics = self.compute_loss(batch)
        
        # 记录训练指标
        self.log('train_loss', loss, on_step=True, on_epoch=True)
        for key, value in metrics.items():
            self.log(f'train_{key}', value, on_step=True, on_epoch=True)
        
        # 记录学习率
        self.log('lr', self.optimizers().param_groups[0]['lr'], on_step=True)
        
        return loss
    
    def validation_step(self, batch, batch_idx):
        # 验证步骤
        loss, metrics = self.compute_loss(batch)
        
        # 记录验证指标
        self.log('val_loss', loss, on_epoch=True)
        for key, value in metrics.items():
            self.log(f'val_{key}', value, on_epoch=True)
        
        # 记录图像（每个epoch的第一个batch）
        if batch_idx == 0:
            self.log_images(batch)
        
        return loss
    
    def log_images(self, batch):
        """记录图像到W&B"""
        import wandb
        
        low_images, normal_images = batch['low'], batch['normal']
        with torch.no_grad():
            enhanced_images = self.model(low_images)
        
        # 选择前4张图像
        n_images = min(4, low_images.size(0))
        
        images = []
        for i in range(n_images):
            # 反标准化
            low_img = self.denormalize(low_images[i])
            normal_img = self.denormalize(normal_images[i])
            enhanced_img = self.denormalize(enhanced_images[i])
            
            # 创建对比图像
            comparison = torch.cat([low_img, enhanced_img, normal_img], dim=2)
            
            images.append(wandb.Image(
                comparison.cpu().numpy().transpose(1, 2, 0),
                caption=f"样本 {i+1}: 低光 | 增强 | 参考"
            ))
        
        self.logger.experiment.log({"验证样本": images})
```

### 本地日志记录

```python
# 使用Loguru进行本地日志记录
from loguru import logger
import sys

# 配置日志
logger.remove()  # 移除默认处理器
logger.add(
    sys.stdout,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    level="INFO"
)
logger.add(
    "outputs/logs/training_{time}.log",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
    level="DEBUG",
    rotation="100 MB"
)

# 在训练代码中使用
logger.info("开始训练...")
logger.debug(f"批次大小: {batch_size}")
logger.warning("GPU内存使用率较高")
logger.error("训练过程中出现错误")
```

### 训练进度可视化

```python
# 使用Rich创建美观的进度条
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.console import Console

console = Console()

def create_training_progress():
    """创建训练进度条"""
    return Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        TimeElapsedColumn(),
        console=console
    )

# 在训练循环中使用
with create_training_progress() as progress:
    epoch_task = progress.add_task("训练进度", total=max_epochs)
    
    for epoch in range(max_epochs):
        batch_task = progress.add_task(f"Epoch {epoch+1}", total=len(train_loader))
        
        for batch_idx, batch in enumerate(train_loader):
            # 训练步骤
            loss = train_step(batch)
            
            # 更新进度
            progress.update(batch_task, advance=1)
            progress.update(epoch_task, description=f"训练进度 (Loss: {loss:.4f})")
        
        progress.remove_task(batch_task)
        progress.update(epoch_task, advance=1)
```

## 🐛 调试和故障排除

### 常见训练问题

#### 1. 显存不足 (CUDA Out of Memory)

```python
# 解决方案1：减少批次大小
python run.py trainer.batch_size=4

# 解决方案2：启用梯度累积
python run.py trainer.batch_size=4 trainer.accumulate_grad_batches=4

# 解决方案3：启用混合精度
python run.py trainer.mixed_precision=true

# 解决方案4：减少模型大小
python run.py model=dmfourllie_light
```

#### 2. 训练不收敛

```python
# 检查学习率
python run.py optimizer.lr=0.0001  # 降低学习率

# 检查损失函数权重
python run.py trainer.loss.pixel_weight=1.0 trainer.loss.ssim_weight=0.1

# 启用梯度裁剪
python run.py trainer.gradient_clip_val=1.0

# 检查数据预处理
python run.py dataset.normalize=true
```

#### 3. 过拟合

```python
# 增加正则化
python run.py optimizer.weight_decay=0.01

# 启用早停
python run.py trainer.early_stopping.patience=10

# 增加数据增强
python run.py dataset.augmentation.enabled=true

# 减少模型复杂度
python run.py model.architecture.num_blocks=4
```

### 调试工具

```python
# scripts/debug_training.py
import torch
import torch.nn.utils as utils
from src.llie.models import build_model
from src.llie.data import build_dataloader

def debug_model(cfg):
    """调试模型"""
    
    model = build_model(cfg.model)
    
    # 检查模型参数
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"总参数量: {total_params:,}")
    print(f"可训练参数量: {trainable_params:,}")
    
    # 检查模型输出
    dummy_input = torch.randn(1, 3, 256, 256)
    with torch.no_grad():
        output = model(dummy_input)
    
    print(f"输入形状: {dummy_input.shape}")
    print(f"输出形状: {output.shape}")
    
    # 检查梯度流
    model.train()
    loss = torch.nn.functional.mse_loss(output, dummy_input)
    loss.backward()
    
    # 统计梯度
    grad_norms = []
    for name, param in model.named_parameters():
        if param.grad is not None:
            grad_norm = param.grad.data.norm(2)
            grad_norms.append(grad_norm.item())
            if grad_norm == 0:
                print(f"⚠️ 零梯度: {name}")
    
    print(f"平均梯度范数: {sum(grad_norms) / len(grad_norms):.6f}")
    print(f"最大梯度范数: {max(grad_norms):.6f}")

def debug_dataloader(cfg):
    """调试数据加载器"""
    
    train_loader = build_dataloader(cfg.dataset, split='train', batch_size=4)
    
    # 检查数据加载
    for batch_idx, batch in enumerate(train_loader):
        if batch_idx >= 3:  # 只检查前3个批次
            break
        
        low_images = batch['low']
        normal_images = batch['normal']
        
        print(f"批次 {batch_idx}:")
        print(f"  低光图像: {low_images.shape}, 范围: [{low_images.min():.3f}, {low_images.max():.3f}]")
        print(f"  正常图像: {normal_images.shape}, 范围: [{normal_images.min():.3f}, {normal_images.max():.3f}]")
        
        # 检查是否有NaN或Inf
        if torch.isnan(low_images).any():
            print("  ⚠️ 低光图像包含NaN")
        if torch.isinf(low_images).any():
            print("  ⚠️ 低光图像包含Inf")

# 使用示例
if __name__ == "__main__":
    from omegaconf import OmegaConf
    cfg = OmegaConf.load('configs/config.yaml')
    
    debug_model(cfg)
    debug_dataloader(cfg)
```

### 性能分析

```python
# scripts/profile_training.py
import torch
import torch.profiler
from src.llie.models import build_model

def profile_model(cfg):
    """性能分析"""
    
    model = build_model(cfg.model).cuda()
    dummy_input = torch.randn(4, 3, 256, 256).cuda()
    
    # 预热
    for _ in range(10):
        with torch.no_grad():
            _ = model(dummy_input)
    
    # 性能分析
    with torch.profiler.profile(
        activities=[
            torch.profiler.ProfilerActivity.CPU,
            torch.profiler.ProfilerActivity.CUDA,
        ],
        schedule=torch.profiler.schedule(wait=1, warmup=1, active=3, repeat=2),
        on_trace_ready=torch.profiler.tensorboard_trace_handler('./outputs/profiler'),
        record_shapes=True,
        profile_memory=True,
        with_stack=True
    ) as prof:
        for step in range(10):
            with torch.no_grad():
                _ = model(dummy_input)
            prof.step()
    
    print("性能分析完成，结果保存到 ./outputs/profiler")
    print("使用 tensorboard --logdir=./outputs/profiler 查看结果")

# 内存使用分析
def analyze_memory_usage():
    """分析GPU内存使用"""
    
    if torch.cuda.is_available():
        print(f"GPU内存使用情况:")
        print(f"  已分配: {torch.cuda.memory_allocated() / 1024**3:.2f} GB")
        print(f"  已缓存: {torch.cuda.memory_reserved() / 1024**3:.2f} GB")
        print(f"  总容量: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.2f} GB")
```

## 🎛️ 超参数调优

### 网格搜索

```python
# scripts/hyperparameter_search.py
import itertools
from omegaconf import OmegaConf

def grid_search():
    """网格搜索超参数"""
    
    # 定义搜索空间
    search_space = {
        'optimizer.lr': [0.0001, 0.0002, 0.0005],
        'trainer.batch_size': [8, 16, 32],
        'optimizer.weight_decay': [0.01, 0.001, 0.0001],
        'trainer.loss.ssim_weight': [0.1, 0.2, 0.5],
    }
    
    # 生成所有组合
    keys = list(search_space.keys())
    values = list(search_space.values())
    
    best_score = 0
    best_params = None
    
    for combination in itertools.product(*values):
        params = dict(zip(keys, combination))
        
        # 创建配置
        cfg = OmegaConf.load('configs/config.yaml')
        for key, value in params.items():
            OmegaConf.set(cfg, key, value)
        
        # 训练模型
        score = train_with_config(cfg)
        
        if score > best_score:
            best_score = score
            best_params = params
        
        print(f"参数: {params}, 得分: {score:.4f}")
    
    print(f"最佳参数: {best_params}")
    print(f"最佳得分: {best_score:.4f}")

def train_with_config(cfg):
    """使用指定配置训练模型并返回验证得分"""
    # 这里实现训练逻辑
    # 返回验证集上的PSNR或其他指标
    pass
```

### 贝叶斯优化

```python
# 使用Optuna进行贝叶斯优化
import optuna

def objective(trial):
    """优化目标函数"""
    
    # 定义超参数搜索空间
    lr = trial.suggest_float('lr', 1e-5, 1e-2, log=True)
    batch_size = trial.suggest_categorical('batch_size', [8, 16, 32])
    weight_decay = trial.suggest_float('weight_decay', 1e-5, 1e-1, log=True)
    ssim_weight = trial.suggest_float('ssim_weight', 0.01, 1.0)
    
    # 创建配置
    cfg = OmegaConf.load('configs/config.yaml')
    cfg.optimizer.lr = lr
    cfg.trainer.batch_size = batch_size
    cfg.optimizer.weight_decay = weight_decay
    cfg.trainer.loss.ssim_weight = ssim_weight
    
    # 训练模型
    score = train_with_config(cfg)
    
    return score

# 运行优化
study = optuna.create_study(direction='maximize')
study.optimize(objective, n_trials=50)

print(f"最佳参数: {study.best_params}")
print(f"最佳得分: {study.best_value}")
```

### 学习率查找

```python
# 学习率范围测试
def find_learning_rate(cfg):
    """查找最佳学习率"""
    
    model = build_model(cfg.model)
    train_loader = build_dataloader(cfg.dataset, split='train', batch_size=cfg.trainer.batch_size)
    
    # 学习率范围
    lr_min, lr_max = 1e-6, 1e-1
    num_steps = 100
    
    lrs = []
    losses = []
    
    optimizer = torch.optim.AdamW(model.parameters(), lr=lr_min)
    
    for step, batch in enumerate(train_loader):
        if step >= num_steps:
            break
        
        # 指数增长学习率
        lr = lr_min * (lr_max / lr_min) ** (step / num_steps)
        for param_group in optimizer.param_groups:
            param_group['lr'] = lr
        
        # 训练步骤
        optimizer.zero_grad()
        loss = compute_loss(model, batch)
        loss.backward()
        optimizer.step()
        
        lrs.append(lr)
        losses.append(loss.item())
    
    # 绘制学习率-损失曲线
    import matplotlib.pyplot as plt
    
    plt.figure(figsize=(10, 6))
    plt.semilogx(lrs, losses)
    plt.xlabel('学习率')
    plt.ylabel('损失')
    plt.title('学习率查找')
    plt.grid(True)
    plt.savefig('lr_finder.png')
    plt.show()
    
    # 找到最佳学习率（损失下降最快的点）
    gradients = np.gradient(losses)
    best_lr_idx = np.argmin(gradients)
    best_lr = lrs[best_lr_idx]
    
    print(f"建议学习率: {best_lr:.2e}")
    
    return best_lr
```

## 🚀 高级训练技巧

### 渐进式训练

```python
# 渐进式图像尺寸训练
def progressive_training(cfg):
    """渐进式训练：从小图像开始，逐步增大"""
    
    image_sizes = [(128, 128), (256, 256), (512, 512)]
    epochs_per_stage = [20, 30, 50]
    
    model = build_model(cfg.model)
    
    for stage, (size, epochs) in enumerate(zip(image_sizes, epochs_per_stage)):
        print(f"阶段 {stage + 1}: 图像尺寸 {size}, 训练 {epochs} 轮")
        
        # 更新数据加载器
        cfg.dataset.image_size = size
        train_loader = build_dataloader(cfg.dataset, split='train', batch_size=cfg.trainer.batch_size)
        
        # 训练当前阶段
        train_stage(model, train_loader, epochs)
        
        # 保存阶段检查点
        torch.save(model.state_dict(), f'outputs/checkpoints/stage_{stage + 1}.pth')
```

### 知识蒸馏

```python
# 知识蒸馏训练
class DistillationLoss(nn.Module):
    """知识蒸馏损失"""
    
    def __init__(self, alpha=0.7, temperature=4):
        super().__init__()
        self.alpha = alpha
        self.temperature = temperature
        self.kl_div = nn.KLDivLoss(reduction='batchmean')
        self.mse_loss = nn.MSELoss()
    
    def forward(self, student_output, teacher_output, target):
        # 硬目标损失
        hard_loss = self.mse_loss(student_output, target)
        
        # 软目标损失
        soft_student = F.log_softmax(student_output / self.temperature, dim=1)
        soft_teacher = F.softmax(teacher_output / self.temperature, dim=1)
        soft_loss = self.kl_div(soft_student, soft_teacher) * (self.temperature ** 2)
        
        return self.alpha * soft_loss + (1 - self.alpha) * hard_loss

def distillation_training(teacher_model, student_model, train_loader):
    """知识蒸馏训练"""
    
    teacher_model.eval()  # 教师模型不更新
    student_model.train()
    
    optimizer = torch.optim.AdamW(student_model.parameters(), lr=0.0002)
    distill_loss = DistillationLoss()
    
    for batch in train_loader:
        low_images, normal_images = batch['low'], batch['normal']
        
        # 教师模型预测
        with torch.no_grad():
            teacher_output = teacher_model(low_images)
        
        # 学生模型预测
        student_output = student_model(low_images)
        
        # 计算蒸馏损失
        loss = distill_loss(student_output, teacher_output, normal_images)
        
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
```

### 对抗训练

```python
# 对抗训练增强鲁棒性
def adversarial_training(model, train_loader, epsilon=0.01):
    """对抗训练"""
    
    optimizer = torch.optim.AdamW(model.parameters(), lr=0.0002)
    
    for batch in train_loader:
        low_images, normal_images = batch['low'], batch['normal']
        low_images.requires_grad_(True)
        
        # 正常训练步骤
        model.zero_grad()
        output = model(low_images)
        loss = F.mse_loss(output, normal_images)
        loss.backward()
        
        # 生成对抗样本
        data_grad = low_images.grad.data
        perturbed_data = low_images + epsilon * data_grad.sign()
        perturbed_data = torch.clamp(perturbed_data, 0, 1)
        
        # 对抗样本训练
        model.zero_grad()
        adv_output = model(perturbed_data.detach())
        adv_loss = F.mse_loss(adv_output, normal_images)
        
        # 总损失
        total_loss = loss + 0.5 * adv_loss
        total_loss.backward()
        optimizer.step()
```

## 🌐 分布式训练

### 多GPU训练

```bash
# 使用PyTorch Lightning的DDP
python run.py trainer.devices=4 trainer.strategy=ddp

# 使用特定GPU
CUDA_VISIBLE_DEVICES=0,1,2,3 python run.py trainer.devices=4

# 混合精度 + 多GPU
python run.py trainer.devices=4 trainer.strategy=ddp trainer.mixed_precision=true
```

### 分布式配置

```yaml
# configs/trainer/distributed.yaml
devices: 4                        # GPU数量
strategy: "ddp"                   # 分布式策略
sync_batchnorm: true              # 同步BatchNorm
find_unused_parameters: false     # 查找未使用参数

# 优化器配置（需要调整学习率）
optimizer:
  lr: 0.0008                      # 4倍学习率（4个GPU）
  
# 批次大小配置
trainer:
  batch_size: 16                  # 每个GPU的批次大小
```

### 自定义分布式训练

```python
# scripts/distributed_train.py
import torch.distributed as dist
import torch.multiprocessing as mp
from torch.nn.parallel import DistributedDataParallel as DDP

def setup(rank, world_size):
    """初始化分布式环境"""
    os.environ['MASTER_ADDR'] = 'localhost'
    os.environ['MASTER_PORT'] = '12355'
    dist.init_process_group("nccl", rank=rank, world_size=world_size)

def cleanup():
    """清理分布式环境"""
    dist.destroy_process_group()

def train_worker(rank, world_size, cfg):
    """分布式训练worker"""
    
    setup(rank, world_size)
    
    # 设置设备
    torch.cuda.set_device(rank)
    device = torch.device(f'cuda:{rank}')
    
    # 创建模型
    model = build_model(cfg.model).to(device)
    model = DDP(model, device_ids=[rank])
    
    # 创建分布式数据加载器
    train_sampler = torch.utils.data.distributed.DistributedSampler(
        train_dataset, num_replicas=world_size, rank=rank
    )
    train_loader = DataLoader(
        train_dataset, batch_size=cfg.trainer.batch_size,
        sampler=train_sampler, num_workers=4
    )
    
    # 训练循环
    for epoch in range(cfg.trainer.max_epochs):
        train_sampler.set_epoch(epoch)
        
        for batch in train_loader:
            # 训练步骤
            pass
    
    cleanup()

def main():
    """主函数"""
    world_size = 4
    mp.spawn(train_worker, args=(world_size, cfg), nprocs=world_size, join=True)

if __name__ == "__main__":
    main()
```

## 📈 训练监控最佳实践

### 关键指标监控

```python
# 监控的关键指标
metrics_to_monitor = {
    'loss': {
        'train_loss': '训练损失',
        'val_loss': '验证损失',
        'train_pixel_loss': '像素损失',
        'train_ssim_loss': 'SSIM损失',
    },
    'quality': {
        'val_psnr': 'PSNR',
        'val_ssim': 'SSIM',
        'val_mae': 'MAE',
        'val_lpips': 'LPIPS',
    },
    'training': {
        'lr': '学习率',
        'grad_norm': '梯度范数',
        'epoch_time': '每轮时间',
        'gpu_memory': 'GPU内存使用',
    }
}
```

### 异常检测

```python
# 训练异常检测
class TrainingMonitor:
    """训练监控器"""
    
    def __init__(self):
        self.loss_history = []
        self.grad_norm_history = []
        
    def check_training_health(self, loss, grad_norm):
        """检查训练健康状态"""
        
        self.loss_history.append(loss)
        self.grad_norm_history.append(grad_norm)
        
        warnings = []
        
        # 检查损失爆炸
        if loss > 10 * np.mean(self.loss_history[-10:]):
            warnings.append("⚠️ 损失爆炸")
        
        # 检查梯度爆炸
        if grad_norm > 100:
            warnings.append("⚠️ 梯度爆炸")
        
        # 检查梯度消失
        if grad_norm < 1e-6:
            warnings.append("⚠️ 梯度消失")
        
        # 检查训练停滞
        if len(self.loss_history) > 20:
            recent_losses = self.loss_history[-20:]
            if np.std(recent_losses) < 1e-6:
                warnings.append("⚠️ 训练停滞")
        
        return warnings
```

---

本教程涵盖了LLIE项目中模型训练的各个方面。通过遵循这些指导原则和最佳实践，您应该能够成功训练出高质量的低光图像增强模型。如有问题，请参考[故障排除指南](troubleshooting.md)或提交Issue。

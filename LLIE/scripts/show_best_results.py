#!/usr/bin/env python3
"""
显示最佳实验结果的命令行工具
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.llie.utils.experiment_manager import ExperimentManager


def main():
    """主函数"""
    print("🏆 LLIE Project - Best Results Viewer")
    print("=" * 50)

    try:
        # 初始化实验管理器
        manager = ExperimentManager()

        # 显示最佳结果
        manager.display_best_results()

        # 显示历史记录统计
        history = manager.get_experiment_history()
        if len(history) > 1:
            print(f"\n📈 Total experiments evaluated: {len(history)}")

            # 显示评分趋势
            scores = [exp.get("comprehensive_score", 0) for exp in history]
            if scores:
                print(f"📊 Score range: {min(scores):.2f} - {max(scores):.2f}")
                print(f"📈 Average score: {sum(scores) / len(scores):.2f}")

    except Exception as e:
        print(f"❌ Error: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())

# CLAUDE.md

本文件为 Claude Code (claude.ai/code) 在此代码仓库中工作时提供指导。

## 项目概述

本项目是从 [DMFourLLIE](https://github.com/bywlzts/DMFourLLIE) 原始项目重构而来的现代化低光图像增强 (LLIE) 研究框架。重构遵循高度模块化、可读性和现代化工具链的设计原则，实现了基于配置驱动的实验管理框架。

**重构核心特点**：
- 🏗️ **高度模块化**：基于注册器的"即插即用"机制，便于实验和模块替换
- 📚 **教学友好**：中文注释，清晰结构，适合深度学习初学者学习
- 🛠️ **现代化工具链**：Loguru、Hydra、W&B、Rich 等现代化开发工具
- ⚙️ **配置驱动**：所有参数通过 YAML 配置文件管理，支持实验追踪
- 🔍 **实验记录**：系统化的实验管理和结果追踪

## 开发命令

### 环境设置（使用 UV）
```bash
# 使用 UV 创建虚拟环境
uv venv
source .venv/bin/activate  # Linux/Mac
# 或 .venv\Scripts\activate  # Windows

# 安装项目依赖（从 pyproject.toml）
uv pip install -e .

# 可选：安装完整开发环境
uv pip install -e ".[full]"
```

### 主要任务
```bash
# 使用默认配置训练
uv run main.py task=train

# 使用自定义参数训练
uv run main.py task=train trainer.batch_size=8 trainer.max_epochs=50

# 模型评估
uv run main.py task=evaluate evaluation.checkpoint_path=outputs/models/best_model.pth

# 图像推理
uv run main.py task=inference inference.checkpoint_path=outputs/models/best_model.pth inference.input_path=path/to/image.jpg
```

### 开发工具（使用 UV）
```bash
# 代码格式化和检查
uv run ruff check src/llie/
uv run ruff format src/llie/

# 类型检查
uv run mypy src/llie/

# 测试
uv run pytest tests/

# 运行所有质量检查
uv run ruff check src/llie/ && uv run mypy src/llie/ && uv run pytest tests/
```

## 架构概览

### 核心组件

**任务系统** (`src/llie/tasks/`):
- `BaseTask`: 所有任务的抽象基类
- `TrainTask`: 使用 ModernTrainer 处理模型训练
- `evaluate_task`: 模型评估与综合指标
- `inference_task`: 图像增强推理

**模型架构** (`src/llie/models/`):
- `BaseArchitecture`: 所有模型的抽象基类
- `LLIE`: 实现双分支两阶段的主模型：
    - 第一阶段，专注于傅里叶重建。在傅里叶频域中，旨在同时增强幅度和相位分量的表达能力
      - 对于振幅分量，则通过使用亮度注意力图实现亮度空间的转换，从而提高不同亮度水平下的放大精度。
    - 第二阶段，重点转向空间结构与纹理的重建。此阶段引入了一个双路径架构，结合了多尺度空间卷积分支基于傅里叶卷积的分支。该配置旨在优化空间结构的表达，同时捕捉细腻的纹理细节

- `components/`: 可重用的模型组件（FFC 块、傅里叶块等）

**数据流水线** (`src/llie/data/`):
- `dataloader.py`: 创建训练/验证数据加载器
- `dataset.py`: 基础数据集类
- `lol_dataset.py`: LOL 数据集实现
- `transforms.py`: 数据增强和预处理

**训练引擎** (`src/llie/engine/`):
- `trainer.py`: ModernTrainer，支持混合精度、EMA、早停

**工具类** (`src/llie/utils/`):
- `registry.py`: 组件注册系统（核心"即插即用"机制）
- `experiment_manager.py`: 统一的实验管理接口
- `storage/`: 智能的模型和输出管理系统
  - `model_manager.py`: 模型文件管理和软链接优化
  - `output_manager.py`: 输出目录组织
  - `result_manager.py`: 评估结果管理
- `logging/`: 基于 Rich 的结构化日志系统
  - `experiment_logger.py`: 实验生命周期日志记录
  - `progress_tracker.py`: 美观的训练进度可视化
  - `config_formatter.py`: 配置显示和序列化

### 配置系统

项目使用 Hydra 进行层次化配置管理：
- `configs/config.yaml`: 主配置文件
- `configs/model/`: 模型架构配置
- `configs/dataset/`: 数据集配置
- `configs/trainer/`: 训练参数
- `configs/task/`: 任务特定配置

### 关键设计模式

1. **注册表模式**: 使用装饰器注册组件（`@register_model`、`@register_dataset`），实现真正的"即插即用"
2. **配置驱动**: 所有行为通过 Hydra 配置控制，支持实验追踪和复现
3. **基于任务的架构**: 训练/评估/推理任务的独立类，统一入口管理
4. **模块化组件**: 具有清晰接口的可重用模型组件，便于实验和扩展
5. **依赖注入**: 组件间松耦合设计，易于测试和维护
6. **单一职责**: 每个类专注一个核心功能，避免过度工程化

## 重要文件

### 入口点
- `main.py`: 使用 Hydra 的主应用程序入口点
- `src/llie/__init__.py`: 包初始化

### 配置文件
- `configs/config.yaml`: 包含组件组合的主配置
- `pyproject.toml`: 项目依赖和工具配置

### 核心实现
- `src/llie/models/llie.py`: LLIE 模型实现
- `src/llie/tasks/train_task.py`: 训练任务逻辑
- `src/llie/engine/trainer.py`: 现代化训练引擎
- `src/llie/utils/registry.py`: 组件注册系统

## 开发工作流

### 添加新模型
1. 创建继承自 `BaseArchitecture` 的模型类
2. 使用 `@register_model("your_model_name")` 装饰器
3. 在 `configs/model/` 中添加配置文件
4. 在 `configs/config.yaml` 默认值中注册

### 添加新数据集
1. 遵循现有模式创建数据集类
2. 使用 `@register_dataset("your_dataset")` 装饰器
3. 在 `configs/dataset/` 中添加配置文件
4. 如需要，更新数据加载逻辑

### 配置覆盖示例
```bash
# 修改模型架构
python main.py model.architecture.s_nf=64 model.architecture.num_blocks=8

# 修改训练参数
python main.py trainer.batch_size=16 trainer.optimizer.lr=0.002

# 启用混合精度训练
python main.py trainer.use_amp=true

# 超参数扫描
python main.py --multirun trainer.optimizer.lr=0.001,0.002,0.005
```

## 输出结构

项目使用带时间戳的输出目录：
- `outputs/train/`: 训练结果，包含检查点和日志
- `outputs/evaluate/`: 评估结果，包含指标和对比图
- `outputs/inference/`: 推理结果，包含增强后的图像
- `outputs/models/`: 按数据集和指标组织的最佳模型

## 重构特色

### 教学友好设计
- **中文注释**: 所有代码注释和文档字符串使用中文，便于学习理解
- **清晰结构**: 代码分层明确，职责分离，易于理解
- **避免过度工程化**: 保持简洁实用，适合初学者学习

### 现代化工具链
- **包管理**: UV (现代 Python 包管理器)
- **日志系统**: Loguru (美观的结构化日志)
- **实验跟踪**: Weights & Biases (可视化实验追踪)
- **进度显示**: Rich (高质量控制台输出)
- **配置管理**: Hydra (层次化配置系统)

## 代码风格和质量

- **格式化**: Ruff 处理代码检查和格式化（替代 black+isort+flake8）
- **类型安全**: MyPy 严格类型检查，提高代码健壮性
- **测试**: Pytest 与覆盖率报告，确保代码质量
- **文档**: 全面的中文文档字符串，便于学习和维护

## 关键依赖

- **深度学习**: PyTorch 2.0+, torchvision, einops
- **配置管理**: Hydra, OmegaConf, PyYAML
- **图像处理**: OpenCV, Pillow, Albumentations
- **评估指标**: LPIPS, pytorch-msssim, scikit-image
- **日志系统**: Loguru, Rich, tqdm
- **科学计算**: NumPy, SciPy, Pandas
- **开发工具**: Ruff, MyPy, pytest
- **实验跟踪**: Weights & Biases, Tensorboard
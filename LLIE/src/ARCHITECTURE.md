# LLIE 项目模块依赖关系图

## 项目架构 Mermaid 图

```mermaid
graph TB
    %% 主入口点
    main[main.py<br/>主入口点]
    
    %% 任务模块
    subgraph Tasks[任务管理 tasks/]
        base_task[BaseTask<br/>抽象基类]
        train_task[TrainTask<br/>训练任务]
        eval_task[EvaluateTask<br/>评估任务]
        infer_task[InferenceTask<br/>推理任务]
    end
    
    %% 模型模块
    subgraph Models[模型架构 models/]
        base_model[BaseArchitecture<br/>模型基类]
        llie_model[DMFourLLIE<br/>主模型]
        
        subgraph Components[模型组件 components/]
            luminance[LuminanceMapProcessor<br/>亮度图处理]
            fourier[FourierProcessor<br/>傅里叶处理]
            ffc[FFC Blocks<br/>快速傅里叶卷积]
            common[Common Components<br/>通用组件]
        end
    end
    
    %% 数据模块
    subgraph Data[数据处理 data/]
        base_dataset[BaseDataset<br/>数据集基类]
        lol_dataset[LOLDataset<br/>LOL数据集]
        dataloader[DataLoader<br/>数据加载器]
        transforms[Transforms<br/>数据变换]
    end
    
    %% 训练引擎
    subgraph Engine[训练引擎 engine/]
        trainer[ModernTrainer<br/>现代训练器]
    end
    
    %% 损失函数
    subgraph Losses[损失函数 losses/]
        charbonnier[CharbonnierLoss<br/>感知损失]
        perceptual[PerceptualLoss<br/>感知损失]
        ssim_loss[SSIMLoss<br/>结构相似性损失]
        edge_loss[EdgeLoss<br/>边缘损失]
        color_loss[ColorLoss<br/>颜色损失]
        combined[CombinedLoss<br/>组合损失]
    end
    
    %% 工具模块
    subgraph Utils[工具模块 utils/]
        subgraph Registry[注册系统 registry.py]
            models_reg[MODELS Registry<br/>模型注册]
            datasets_reg[DATASETS Registry<br/>数据集注册]
            optimizers_reg[OPTIMIZERS Registry<br/>优化器注册]
            schedulers_reg[SCHEDULERS Registry<br/>调度器注册]
            losses_reg[LOSSES Registry<br/>损失注册]
            metrics_reg[METRICS Registry<br/>指标注册]
        end
        
        subgraph Logging[日志系统 logging/]
            exp_logger[ExperimentLogger<br/>实验日志]
            progress[ProgressTracker<br/>进度跟踪]
            config_fmt[ConfigFormatter<br/>配置格式化]
        end
        
        subgraph Storage[存储管理 storage/]
            model_mgr[ModelManager<br/>模型管理]
            output_mgr[OutputManager<br/>输出管理]
            result_mgr[ResultManager<br/>结果管理]
            path_mgr[PathManager<br/>路径管理]
        end
        
        exp_mgr[ExperimentManager<br/>实验管理器]
        metrics[Metrics<br/>评估指标]
        image_utils[ImageUtils<br/>图像工具]
        optimizers[Optimizers<br/>优化器工具]
        schedulers[Schedulers<br/>调度器工具]
        model_analysis[ModelAnalysis<br/>模型分析]
    end
    
    %% 配置系统
    subgraph Configs[配置系统 configs/]
        main_config[config.yaml<br/>主配置]
        task_configs[task/*.yaml<br/>任务配置]
        model_configs[model/*.yaml<br/>模型配置]
        dataset_configs[dataset/*.yaml<br/>数据集配置]
        trainer_configs[trainer/*.yaml<br/>训练器配置]
    end
    
    %% 依赖关系
    main --> Configs
    main --> base_task
    
    %% 任务依赖
    base_task --> trainer
    base_task --> models_reg
    base_task --> datasets_reg
    train_task --> base_task
    eval_task --> base_task
    infer_task --> base_task
    
    %% 训练器依赖
    trainer --> base_model
    trainer --> optimizers_reg
    trainer --> schedulers_reg
    trainer --> losses_reg
    trainer --> metrics_reg
    trainer --> exp_logger
    trainer --> progress
    trainer --> model_mgr
    
    %% 模型依赖
    base_model --> Components
    llie_model --> base_model
    llie_model --> luminance
    llie_model --> fourier
    llie_model --> ffc
    llie_model --> common
    
    %% 数据依赖
    base_dataset --> transforms
    lol_dataset --> base_dataset
    dataloader --> datasets_reg
    dataloader --> base_dataset
    
    %% 损失函数依赖
    charbonnier --> losses_reg
    perceptual --> losses_reg
    ssim_loss --> losses_reg
    edge_loss --> losses_reg
    color_loss --> losses_reg
    combined --> losses_reg
    
    %% 工具依赖
    models_reg --> base_model
    datasets_reg --> base_dataset
    optimizers_reg --> optimizers
    schedulers_reg --> schedulers
    losses_reg --> Losses
    metrics_reg --> metrics
    
    exp_mgr --> result_mgr
    exp_mgr --> Storage
    exp_logger --> Logging
    progress --> Logging
    config_fmt --> Logging
    
    %% 样式定义
    classDef mainEntry fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef taskModule fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef modelModule fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef dataModule fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef engineModule fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef lossModule fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef utilModule fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef configModule fill:#efebe9,stroke:#3e2723,stroke-width:2px
    
    class base_model modelModule
    class llie_model modelModule
    class luminance modelModule
    class fourier modelModule
    class ffc modelModule
    class common modelModule

    class base_dataset,lol_dataset,dataloader,transforms dataModule
    class trainer engineModule
    class charbonnier,perceptual,ssim_loss,edge_loss,color_loss,combined lossModule
    class Registry,Logging,Storage,exp_mgr,metrics,image_utils,optimizers,schedulers,model_analysis utilModule
    class main_config,task_configs,model_configs,dataset_configs,trainer_configs configModule
```

## 模块依赖关系说明

### 1. **入口层 (Entry Point)**
- `main.py` 是整个系统的入口点，负责 Hydra 配置加载和任务调度

### 2. **任务层 (Task Layer)**
- 所有任务继承自 `BaseTask`
- `TrainTask`、`EvaluateTask`、`InferenceTask` 分别处理不同工作流
- 依赖训练引擎、注册系统和配置管理

### 3. **模型层 (Model Layer)**
- `BaseArchitecture` 定义所有模型的统一接口
- `DMFourLLIE` 是主模型，依赖各种组件
- 组件模块实现可重用的功能块

### 4. **数据层 (Data Layer)**
- `BaseDataset` 提供数据集的抽象接口
- `LOLDataset` 实现具体的数据集逻辑
- `DataLoader` 通过注册系统动态创建数据加载器

### 5. **引擎层 (Engine Layer)**
- `ModernTrainer` 提供现代化的训练功能
- 依赖所有注册系统来动态构建组件

### 6. **工具层 (Utility Layer)**
- **注册系统**: 核心组件注册和动态构建
- **日志系统**: 结构化日志和进度跟踪
- **存储系统**: 模型和结果管理
- **工具函数**: 评估指标、图像处理等

### 7. **配置层 (Configuration Layer)**
- Hydra 配置系统管理所有参数
- 支持模块化配置和运行时覆盖

## 数据流向

```mermaid
sequenceDiagram
    participant Main as main.py
    participant Task as Task
    participant Trainer as ModernTrainer
    participant Model as DMFourLLIE
    participant Data as DataLoader
    participant Utils as Tools
    
    Main->>Task: 执行任务
    Task->>Trainer: 开始训练
    Trainer->>Data: 获取数据批次
    Data->>Model: 输入低光图像
    Model->>Model: 四级增强处理
    Model->>Trainer: 返回增强图像
    Trainer->>Utils: 计算损失和指标
    Trainer->>Trainer: 反向传播和优化
    Utils->>Utils: 日志记录和模型保存
```

## 关键特点

1. **松耦合设计**: 各模块通过注册系统解耦，易于替换和扩展
2. **配置驱动**: 所有行为通过 YAML 配置控制
3. **模块化架构**: 每个模块职责单一，接口清晰
4. **动态构建**: 运行时动态创建组件，支持实验灵活性
5. **统一管理**: 通过工具模块统一管理日志、存储和实验
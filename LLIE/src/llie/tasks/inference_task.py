"""
LLIE框架的推理任务实现

本模块实现了低光图像增强模型的推理任务，提供便捷的图像增强功能：
- 单张图像或批量图像处理
- 自动路径检测和配置
- 模型检查点加载
- 图像预处理和后处理
- 结果保存和摘要生成

推理任务支持多种输入格式，并自动处理路径配置，非常适合生产环境部署。
"""

from omegaconf import DictConfig
from loguru import logger
import torch
from pathlib import Path
from PIL import Image
import torchvision.transforms as T
import numpy as np

from ..utils.registry import MODELS


def run(cfg: DictConfig) -> None:
    """
    运行推理任务

    这是推理任务的主入口函数，处理低光图像的增强推理：
    1. 环境设置和路径配置
    2. 模型构建和检查点加载
    3. 输入图像路径处理（支持自动检测）
    4. 图像预处理管道设置
    5. 批量图像推理处理
    6. 增强图像保存
    7. 推理摘要生成

    参数:
        cfg: Hydra配置，包含推理任务的所有设置：
            - device: 计算设备
            - model: 模型配置
            - inference: 推理特定配置
                - input_path: 输入图像路径（可选，支持自动检测）
                - checkpoint_path: 模型检查点路径（可选，使用最新模型）

    特点:
        - 支持单张图像或整个目录处理
        - 自动检测常见数据集路径
        - 智能回退机制
        - 完整的日志记录
    """
    logger.info("🚀 Initializing Inference Task")

    # --- 1. Setup ---
    device = torch.device(cfg.device)

    # 输入路径处理 - 支持多个候选路径
    input_path_config = cfg.inference.get("input_path")
    if input_path_config:
        input_path = Path(input_path_config)
    else:
        # 尝试多个候选路径
        candidate_paths = [
            "./data/LOLv2/Real_captured/Test/Low",  # LOLv2数据集测试路径
            "./data/test",  # 通用测试路径
            "./data/LOLv2/Real_captured/Train/Low",  # LOLv2训练路径（备选）
        ]

        input_path = None
        for candidate in candidate_paths:
            candidate_path = Path(candidate)
            if candidate_path.exists() and any(candidate_path.glob("*.png")):
                input_path = candidate_path
                logger.info(f"自动检测到输入路径: {input_path}")
                break

        if input_path is None:
            logger.error("未找到有效的输入图像路径！")
            logger.error("请使用以下方式之一指定输入路径：")
            logger.error("1. python main.py task=inference inference.input_path=/path/to/images")
            logger.error("2. 确保以下路径之一存在且包含图像文件：")
            for candidate in candidate_paths:
                logger.error(f"   - {candidate}")
            raise FileNotFoundError("未找到有效的输入图像路径")

    checkpoint_path = cfg.inference.get("checkpoint_path")

    # 如果没有指定checkpoint_path，尝试使用最新的模型
    if not checkpoint_path:
        latest_model_path = Path("./outputs/models/latest_checkpoint.pth")
        if latest_model_path.exists():
            checkpoint_path = str(latest_model_path)
            logger.info(f"自动使用最新模型: {checkpoint_path}")
        else:
            logger.warning("未找到最新模型文件，将使用随机权重")

    # 设置输出目录 - 直接使用Hydra提供的任务分离路径
    try:
        from hydra.core.hydra_config import HydraConfig
        from ..utils.storage import OutputManager
        from ..utils.storage.path_manager import PathManager
        from ..utils.logging import ExperimentLogger

        hydra_cfg = HydraConfig.get()
        experiment_dir = Path(hydra_cfg.runtime.output_dir)
        logger.info(f"使用Hydra推理输出目录: {experiment_dir}")

        # 创建标准目录结构
        path_manager = PathManager(experiment_dir.parent.parent)
        path_manager._create_standard_subdirs(experiment_dir, "inference")

        # 推理任务直接保存到experiment_dir，不创建额外的results子目录
        output_dir = experiment_dir

        # 设置实验日志记录器
        experiment_name = experiment_dir.name
        exp_logger = ExperimentLogger(experiment_name, experiment_dir)

        # 记录实验开始并保存配置文件
        from omegaconf import OmegaConf
        config_dict = OmegaConf.to_container(cfg, resolve=True)
        exp_logger.log_experiment_start(config_dict)

        # 创建OutputManager
        output_manager = OutputManager(str(experiment_dir.parent.parent), task_type="inference")

        # 为推理任务创建模型软链接
        if checkpoint_path and Path(checkpoint_path).exists():
            models_dir = experiment_dir / "models"
            output_manager.create_model_symlink(checkpoint_path, models_dir, "inference_model.pth")
            logger.info(f"为推理任务创建模型软链接: {checkpoint_path}")

    except Exception as e:
        logger.warning(f"Hydra配置获取失败: {e}")
        # 回退到手动创建目录
        from ..utils.storage import OutputManager
        from ..utils.logging import ExperimentLogger

        output_manager = OutputManager("./outputs", task_type="inference")
        experiment_dir = output_manager.create_experiment_directory("inference")
        output_dir = experiment_dir

        # 设置实验日志记录器
        experiment_name = experiment_dir.name
        exp_logger = ExperimentLogger(experiment_name, experiment_dir)

        # 记录实验开始并保存配置文件
        from omegaconf import OmegaConf
        config_dict = OmegaConf.to_container(cfg, resolve=True)
        exp_logger.log_experiment_start(config_dict)

        # 为推理任务创建模型软链接
        if checkpoint_path and Path(checkpoint_path).exists():
            models_dir = experiment_dir / "models"
            output_manager.create_model_symlink(checkpoint_path, models_dir, "inference_model.pth")
            logger.info(f"为推理任务创建模型软链接: {checkpoint_path}")

    logger.info(f"推理结果将保存到: {output_dir}")

    # --- 2. Build Model ---
    logger.info("Building model...")
    from omegaconf import OmegaConf
    model_config = OmegaConf.to_container(cfg.model, resolve=True)
    model = MODELS.build(model_config)
    model.to(device)

    # --- 3. Load Checkpoint ---
    if checkpoint_path:
        logger.info(f"Loading checkpoint from: {checkpoint_path}")
        checkpoint = torch.load(checkpoint_path, map_location=device, weights_only=False)
        if "model_state_dict" in checkpoint:
            model.load_state_dict(checkpoint["model_state_dict"])
        else:
            model.load_state_dict(checkpoint)
    else:
        logger.warning("No checkpoint path provided, using random weights")

    model.eval()

    # --- 4. Process Images ---
    if input_path.is_file():
        # Single image
        image_paths = [input_path]
    else:
        # Directory of images
        image_paths = []
        for ext in ["*.png", "*.jpg", "*.jpeg", "*.bmp", "*.tiff"]:
            image_paths.extend(input_path.glob(ext))
            image_paths.extend(input_path.glob(ext.upper()))

    logger.info(f"Found {len(image_paths)} images to process.")

    # Image preprocessing
    transform = T.Compose(
        [
            T.ToTensor(),
            T.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5]),  # Normalize to [-1, 1]
        ]
    )

    def save_tensor_as_image(tensor, path):
        """
        将张量保存为图像文件

        参数:
            tensor: 图像张量，范围[-1, 1]
            path: 保存路径
        """
        # Denormalize from [-1, 1] to [0, 1]
        tensor = (tensor + 1.0) / 2.0
        tensor = torch.clamp(tensor, 0, 1)

        # Convert to numpy
        img_np = tensor.cpu().numpy().transpose(1, 2, 0)
        img_np = (img_np * 255).astype(np.uint8)

        # Save as PIL Image
        img_pil = Image.fromarray(img_np)
        img_pil.save(path)

    # 创建增强图像保存目录
    enhanced_images_dir = output_dir / "enhanced_images"
    enhanced_images_dir.mkdir(parents=True, exist_ok=True)

    processed_count = 0
    with torch.no_grad():
        for img_path in image_paths:
            logger.info(f"Processing {img_path.name}...")

            # Load and preprocess image
            img = Image.open(img_path).convert("RGB")
            tensor_img = transform(img).unsqueeze(0).to(device)

            # Forward pass
            enhanced_img = model(tensor_img)

            # Save enhanced image
            output_path = enhanced_images_dir / f"enhanced_{img_path.stem}.png"
            save_tensor_as_image(enhanced_img.squeeze(0), output_path)
            logger.info(f"Saved enhanced image to {output_path}")
            processed_count += 1

    # 保存推理摘要
    summary_file = output_dir / "inference_summary.txt"
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(f"推理任务摘要\n")
        f.write(f"=" * 50 + "\n")
        f.write(f"输入路径: {input_path}\n")
        f.write(f"模型路径: {checkpoint_path}\n")
        f.write(f"处理图像数量: {processed_count}\n")
        f.write(f"输出目录: {enhanced_images_dir}\n")
        f.write(f"设备: {device}\n")

    logger.info(f"✅ Inference task completed. Processed {processed_count} images.")
    logger.info(f"📁 Enhanced images saved to: {enhanced_images_dir}")
    logger.info(f"📄 Summary saved to: {summary_file}")

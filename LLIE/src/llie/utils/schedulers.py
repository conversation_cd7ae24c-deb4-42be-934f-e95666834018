"""
LLIE框架的学习率调度器注册。

本模块通过注册系统注册常见的PyTorch学习率调度器。
"""

import torch.optim.lr_scheduler as lr_scheduler
from .registry import register_scheduler


# 注册常见的PyTorch调度器
@register_scheduler("StepLR")
class StepLR(lr_scheduler.StepLR):
    """StepLR调度器。"""

    pass


@register_scheduler("MultiStepLR")
class MultiStepLR(lr_scheduler.MultiStepLR):
    """MultiStepLR调度器。"""

    pass


@register_scheduler("ExponentialLR")
class ExponentialLR(lr_scheduler.ExponentialLR):
    """ExponentialLR调度器。"""

    pass


@register_scheduler("CosineAnnealingLR")
class CosineAnnealingLR(lr_scheduler.CosineAnnealingLR):
    """CosineAnnealingLR调度器。"""

    pass


@register_scheduler("ReduceLROnPlateau")
class ReduceLROnPlateau(lr_scheduler.ReduceLROnPlateau):
    """ReduceLROnPlateau调度器。"""

    pass


@register_scheduler("CyclicLR")
class CyclicLR(lr_scheduler.CyclicLR):
    """CyclicLR调度器。"""

    pass


@register_scheduler("OneCycleLR")
class OneCycleLR(lr_scheduler.OneCycleLR):
    """OneCycleLR调度器。"""

    pass


@register_scheduler("CosineAnnealingWarmRestarts")
class CosineAnnealingWarmRestarts(lr_scheduler.CosineAnnealingWarmRestarts):
    """CosineAnnealingWarmRestarts调度器。"""

    pass


@register_scheduler("MultiplicativeLR")
class MultiplicativeLR(lr_scheduler.MultiplicativeLR):
    """MultiplicativeLR调度器。"""

    pass


@register_scheduler("LambdaLR")
class LambdaLR(lr_scheduler.LambdaLR):
    """LambdaLR调度器。"""

    pass


@register_scheduler("PolynomialLR")
class PolynomialLR(lr_scheduler.PolynomialLR):
    """PolynomialLR调度器。"""

    pass


@register_scheduler("LinearLR")
class LinearLR(lr_scheduler.LinearLR):
    """LinearLR调度器。"""

    pass

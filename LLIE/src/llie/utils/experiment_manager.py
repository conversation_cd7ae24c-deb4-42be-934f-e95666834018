"""
实验管理器 - 重构版

提供简化的实验管理功能，专注于综合评分和结果记录。
使用新的日志和存储系统，避免重复功能。
"""

from pathlib import Path
from typing import Dict, Any, Optional
from loguru import logger

from .logging import ExperimentLogger
from .storage import OutputManager


class ExperimentManager:
    """
    实验管理器 - 简化版

    主要功能：
    1. 计算实验的综合评分
    2. 协调日志记录和文件存储
    3. 提供实验结果的统一管理接口

    设计原则：
    - 职责单一：只负责实验的综合管理，具体功能委托给专门的组件
    - 简洁易用：提供简单的API，隐藏复杂的实现细节
    - 避免重复：不重复实现日志和存储功能
    """

    def __init__(self, base_output_dir: str = "./outputs"):
        """
        初始化实验管理器

        Args:
            base_output_dir: 基础输出目录
        """
        self.base_output_dir = Path(base_output_dir)

        # 使用新的输出管理器
        self.output_manager = OutputManager(str(self.base_output_dir))

        logger.info(f"实验管理器初始化完成: {self.base_output_dir}")

    def create_experiment(
        self, experiment_name: Optional[str] = None
    ) -> tuple[Path, ExperimentLogger]:
        """
        创建新实验

        Args:
            experiment_name: 实验名称（可选）

        Returns:
            (实验目录路径, 实验日志管理器)
        """
        # 创建实验目录
        experiment_dir = self.output_manager.create_experiment_directory(experiment_name)

        # 创建实验日志管理器
        exp_logger = ExperimentLogger(experiment_name or experiment_dir.name, experiment_dir)

        return experiment_dir, exp_logger

    def calculate_comprehensive_score(
        self, metrics: Dict[str, float], weights: Optional[Dict[str, float]] = None
    ) -> float:
        """
        计算综合评分

        Args:
            metrics: 指标字典，包含PSNR、SSIM、LPIPS等
            weights: 权重字典，如果为None则使用默认权重

        Returns:
            综合评分 (0-100)
        """
        # 默认权重配置 - 基于LLIE任务的重要性
        if weights is None:
            weights = {
                "psnr": 0.35,  # PSNR权重35% - 信噪比，重要指标
                "ssim": 0.35,  # SSIM权重35% - 结构相似性，重要指标
                "lpips": 0.20,  # LPIPS权重20% - 感知质量，越小越好
                "mae": 0.10,  # MAE权重10% - 像素误差，越小越好
            }

        score = 0.0
        total_weight = 0.0

        # PSNR评分 (越大越好，正常范围10-40dB)
        if "psnr" in metrics and "psnr" in weights:
            psnr_value = metrics["psnr"]
            # 将PSNR映射到0-100分，10dB对应0分，40dB对应100分
            psnr_score = min(100, max(0, (psnr_value - 10) / 30 * 100))
            score += psnr_score * weights["psnr"]
            total_weight += weights["psnr"]

        # SSIM评分 (越大越好，范围0-1)
        if "ssim" in metrics and "ssim" in weights:
            ssim_value = metrics["ssim"]
            # 直接转换为百分制
            ssim_score = ssim_value * 100
            score += ssim_score * weights["ssim"]
            total_weight += weights["ssim"]

        # LPIPS评分 (越小越好，正常范围0-1)
        if "lpips" in metrics and "lpips" in weights:
            lpips_value = metrics["lpips"]
            # 转换为越大越好的分数
            lpips_score = max(0, (1 - lpips_value) * 100)
            score += lpips_score * weights["lpips"]
            total_weight += weights["lpips"]

        # MAE评分 (越小越好，正常范围0-1)
        if "mae" in metrics and "mae" in weights:
            mae_value = metrics["mae"]
            # 转换为越大越好的分数
            mae_score = max(0, (1 - mae_value) * 100)
            score += mae_score * weights["mae"]
            total_weight += weights["mae"]

        # 归一化评分
        if total_weight > 0:
            score = score / total_weight
        else:
            logger.warning("没有有效的评分指标，返回0分")
            score = 0.0

        return round(score, 2)

    def save_experiment_results(
        self,
        experiment_dir: Path,
        metrics: Dict[str, float],
        model_analysis: Dict[str, Any],
        detailed_results: list,
        config: Optional[Dict[str, Any]] = None,
        image_files: Optional[list] = None,
    ) -> Dict[str, Any]:
        """
        保存实验结果并更新最优模型

        Args:
            experiment_dir: 实验目录路径
            metrics: 评估指标
            model_analysis: 模型分析结果
            detailed_results: 详细结果列表
            config: 实验配置
            image_files: 图片文件列表（可选）

        Returns:
            包含综合评分和更新状态的结果字典
        """
        # 计算综合评分
        comprehensive_score = self.calculate_comprehensive_score(metrics)

        # 使用输出管理器保存结果
        updated_metrics = self.output_manager.save_experiment_results(
            experiment_dir=experiment_dir,
            metrics=metrics,
            model_analysis=model_analysis,
            detailed_results=detailed_results,
            config=config,
            image_files=image_files,
            max_image_samples=50,  # 最多保存50张样例图片
        )

        # 返回结果摘要
        result_summary = {
            "comprehensive_score": comprehensive_score,
            "updated_best_models": updated_metrics,
            "experiment_dir": str(experiment_dir),
            "total_metrics": len(metrics),
        }

        logger.info(f"实验结果已保存，综合评分: {comprehensive_score:.2f}")

        return result_summary

    def save_model_checkpoint(
        self,
        experiment_dir: Path,
        checkpoint_data: Dict[str, Any],
        epoch: int,
        is_final: bool = False,
    ) -> Path:
        """
        保存模型检查点

        Args:
            experiment_dir: 实验目录
            checkpoint_data: 检查点数据
            epoch: 训练轮次
            is_final: 是否为最终模型

        Returns:
            保存的检查点路径
        """
        return self.output_manager.save_experiment_checkpoint(
            experiment_dir, checkpoint_data, epoch, is_final
        )

    def cleanup_experiment(
        self,
        experiment_dir: Path,
        keep_final_model: bool = True,
        keep_evaluation_images: bool = False,
    ) -> None:
        """
        清理实验文件

        Args:
            experiment_dir: 实验目录
            keep_final_model: 是否保留最终模型
            keep_evaluation_images: 是否保留评估图片
        """
        self.output_manager.cleanup_experiment(
            experiment_dir, keep_final_model, False, keep_evaluation_images
        )

    def get_experiment_summary(self) -> Dict[str, Any]:
        """
        获取实验汇总信息

        Returns:
            实验汇总信息字典
        """
        return self.output_manager.get_experiment_summary()

    def find_experiments_by_pattern(self, pattern: str) -> list:
        """
        根据模式查找实验

        Args:
            pattern: 搜索模式

        Returns:
            匹配的实验目录列表
        """
        return self.output_manager.find_experiment_by_pattern(pattern)

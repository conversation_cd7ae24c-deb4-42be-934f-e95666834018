"""
LLIE框架的工具包

本包包含各种实用工具模块，包括：
- 注册表系统：组件管理和配置驱动的实例化
- 日志配置：统一的日志记录和管理
- 可视化工具：结果展示和调试工具
- 通用助手：常用函数和工具类
"""

from .registry import (
    Registry,
    MODELS,
    DATASETS,
    LOSSES,
    OPTIMIZERS,
    SCHEDULERS,
    METRICS,
    TRANSFORMS,
    register_model,
    register_dataset,
    register_loss,
    register_optimizer,
    register_scheduler,
    register_transform,
    register_metric,
)

# Import to trigger registrations
from . import optimizers, schedulers, metrics

__all__ = [
    "Registry",
    "MODELS",
    "DATASETS",
    "LOSSES",
    "OPTIMIZERS",
    "SCHEDULERS",
    "METRICS",
    "TRANSFORMS",
    "register_model",
    "register_dataset",
    "register_loss",
    "register_optimizer",
    "register_scheduler",
    "register_transform",
    "register_metric",
]

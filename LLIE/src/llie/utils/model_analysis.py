"""
模型复杂度分析工具
用于计算模型参数量、FLOPS等复杂度指标
"""

import torch
import torch.nn as nn
from typing import Dict, Any, Tuple, Optional
from loguru import logger
import time
import numpy as np

try:
    from thop import profile, clever_format

    THOP_AVAILABLE = True
except ImportError:
    THOP_AVAILABLE = False
    logger.warning("thop not available, FLOPS calculation will be disabled")


class ModelAnalyzer:
    """模型复杂度分析器"""

    def __init__(self, model: nn.Module, input_size: Tuple[int, ...] = (3, 256, 256)):
        """
        初始化模型分析器

        Args:
            model: 要分析的模型
            input_size: 输入尺寸 (C, H, W)
        """
        self.model = model
        self.input_size = input_size
        self.device = next(model.parameters()).device

    def get_parameter_count(self) -> Dict[str, int]:
        """
        计算模型参数量

        Returns:
            包含参数统计的字典
        """
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        non_trainable_params = total_params - trainable_params

        return {
            "total_parameters": total_params,
            "trainable_parameters": trainable_params,
            "non_trainable_parameters": non_trainable_params,
            "model_size_mb": total_params * 4 / (1024 * 1024),  # 假设float32
        }

    def get_flops(self, batch_size: int = 1) -> Dict[str, Any]:
        """
        计算模型FLOPS

        Args:
            batch_size: 批次大小

        Returns:
            包含FLOPS统计的字典
        """
        if not THOP_AVAILABLE:
            logger.warning("thop not available, returning zero FLOPS")
            return {"flops": 0, "flops_formatted": "0", "params": 0, "params_formatted": "0"}

        # 创建输入张量
        input_tensor = torch.randn(batch_size, *self.input_size).to(self.device)

        # 计算FLOPS
        try:
            flops, params = profile(self.model, inputs=(input_tensor,), verbose=False)
            flops_formatted, params_formatted = clever_format([flops, params], "%.3f")

            return {
                "flops": flops,
                "flops_formatted": flops_formatted,
                "params": params,
                "params_formatted": params_formatted,
                "flops_per_pixel": flops / (self.input_size[1] * self.input_size[2]),
            }
        except Exception as e:
            logger.error(f"FLOPS calculation failed: {e}")
            return {
                "flops": 0,
                "flops_formatted": "Error",
                "params": 0,
                "params_formatted": "Error",
            }

    def measure_inference_time(
        self, num_runs: int = 100, warmup_runs: int = 10
    ) -> Dict[str, float]:
        """
        测量推理时间

        Args:
            num_runs: 测试运行次数
            warmup_runs: 预热运行次数

        Returns:
            包含时间统计的字典
        """
        self.model.eval()
        input_tensor = torch.randn(1, *self.input_size).to(self.device)

        # 预热
        with torch.no_grad():
            for _ in range(warmup_runs):
                _ = self.model(input_tensor)

        # 同步GPU
        if self.device.type == "cuda":
            torch.cuda.synchronize()

        # 测量时间
        times = []
        with torch.no_grad():
            for _ in range(num_runs):
                start_time = time.time()
                _ = self.model(input_tensor)
                if self.device.type == "cuda":
                    torch.cuda.synchronize()
                end_time = time.time()
                times.append(end_time - start_time)

        times = np.array(times)

        return {
            "mean_time": float(np.mean(times)),
            "std_time": float(np.std(times)),
            "min_time": float(np.min(times)),
            "max_time": float(np.max(times)),
            "fps": 1.0 / float(np.mean(times)),
        }

    def get_memory_usage(self) -> Dict[str, float]:
        """
        获取内存使用情况

        Returns:
            包含内存统计的字典
        """
        if self.device.type != "cuda":
            return {"gpu_memory_mb": 0.0}

        # 清空缓存
        torch.cuda.empty_cache()

        # 记录初始内存
        initial_memory = torch.cuda.memory_allocated(self.device) / (1024 * 1024)

        # 前向传播
        input_tensor = torch.randn(1, *self.input_size).to(self.device)
        with torch.no_grad():
            _ = self.model(input_tensor)

        # 记录峰值内存
        peak_memory = torch.cuda.max_memory_allocated(self.device) / (1024 * 1024)
        current_memory = torch.cuda.memory_allocated(self.device) / (1024 * 1024)

        return {
            "initial_memory_mb": initial_memory,
            "peak_memory_mb": peak_memory,
            "current_memory_mb": current_memory,
            "memory_increase_mb": current_memory - initial_memory,
        }

    def analyze_complete(self, batch_size: int = 1, num_runs: int = 100) -> Dict[str, Any]:
        """
        完整的模型分析

        Args:
            batch_size: FLOPS计算的批次大小
            num_runs: 推理时间测试的运行次数

        Returns:
            包含所有分析结果的字典
        """
        logger.info("开始模型复杂度分析...")

        results = {}

        # 参数量分析
        logger.info("计算参数量...")
        results["parameters"] = self.get_parameter_count()

        # FLOPS分析
        logger.info("计算FLOPS...")
        results["flops"] = self.get_flops(batch_size)

        # 推理时间分析
        logger.info("测量推理时间...")
        results["timing"] = self.measure_inference_time(num_runs)

        # 内存使用分析
        logger.info("分析内存使用...")
        results["memory"] = self.get_memory_usage()

        # 添加输入信息
        results["input_info"] = {
            "input_size": self.input_size,
            "batch_size": batch_size,
            "device": str(self.device),
        }

        logger.success("模型复杂度分析完成")
        return results

    def print_summary(self, results: Optional[Dict[str, Any]] = None):
        """
        打印分析结果摘要

        Args:
            results: 分析结果，如果为None则重新分析
        """
        if results is None:
            results = self.analyze_complete()

        logger.info("=" * 60)
        logger.info("模型复杂度分析报告")
        logger.info("=" * 60)

        # 参数信息
        params = results["parameters"]
        logger.info(f"📊 参数统计:")
        logger.info(f"  总参数量: {params['total_parameters']:,}")
        logger.info(f"  可训练参数: {params['trainable_parameters']:,}")
        logger.info(f"  模型大小: {params['model_size_mb']:.2f} MB")

        # FLOPS信息
        flops = results["flops"]
        logger.info(f"⚡ 计算复杂度:")
        logger.info(f"  FLOPS: {flops['flops_formatted']}")
        logger.info(f"  每像素FLOPS: {flops.get('flops_per_pixel', 0):.2f}")

        # 时间信息
        timing = results["timing"]
        logger.info(f"⏱️ 推理性能:")
        logger.info(f"  平均推理时间: {timing['mean_time'] * 1000:.2f} ms")
        logger.info(f"  FPS: {timing['fps']:.2f}")

        # 内存信息
        memory = results["memory"]
        if memory["peak_memory_mb"] > 0:
            logger.info(f"💾 内存使用:")
            logger.info(f"  峰值GPU内存: {memory['peak_memory_mb']:.2f} MB")
            logger.info(f"  内存增量: {memory['memory_increase_mb']:.2f} MB")

        logger.info("=" * 60)


def analyze_model(
    model: nn.Module,
    input_size: Tuple[int, ...] = (3, 256, 256),
    batch_size: int = 1,
    print_summary: bool = True,
) -> Dict[str, Any]:
    """
    便捷的模型分析函数

    Args:
        model: 要分析的模型
        input_size: 输入尺寸
        batch_size: 批次大小
        print_summary: 是否打印摘要

    Returns:
        分析结果字典
    """
    analyzer = ModelAnalyzer(model, input_size)
    results = analyzer.analyze_complete(batch_size)

    if print_summary:
        analyzer.print_summary(results)

    return results

"""
用于评估低光图像增强模型的指标。
包括PSNR、SSIM、LPIPS和其他图像质量指标。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Any, Optional
import lpips

from .registry import register_metric


@register_metric("psnr")
class PSNR(nn.Module):
    """峰值信噪比指标。"""

    def __init__(self, max_val: float = 1.0, reduction: str = "mean"):
        """
        参数:
            max_val: 最大可能像素值。
            reduction: 归约方法 ('mean', 'sum', 'none')。
        """
        super().__init__()
        self.max_val = max_val
        self.reduction = reduction

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        计算预测值和目标值之间的PSNR。

        参数:
            pred: 预测图像 [B, C, H, W]。
            target: 目标图像 [B, C, H, W]。

        返回:
            PSNR值。
        """
        mse = F.mse_loss(pred, target, reduction="none")
        mse = mse.mean(dim=(1, 2, 3))  # Average over C, H, W

        psnr = 20 * torch.log10(self.max_val / torch.sqrt(mse + 1e-8))

        if self.reduction == "mean":
            return psnr.mean()
        elif self.reduction == "sum":
            return psnr.sum()
        else:
            return psnr


@register_metric("ssim")
class SSIM(nn.Module):
    """结构相似性指数度量。"""

    def __init__(
        self, window_size: int = 11, sigma: float = 1.5, k1: float = 0.01, k2: float = 0.03
    ):
        """
        参数:
            window_size: 滑动窗口的大小。
            sigma: 高斯核的标准差。
            k1, k2: SSIM参数。
        """
        super().__init__()
        self.window_size = window_size
        self.sigma = sigma
        self.k1 = k1
        self.k2 = k2

        # 创建高斯核
        self.register_buffer("kernel", self._create_kernel())

    def _create_kernel(self):
        """为SSIM计算创建高斯核。"""
        coords = torch.arange(self.window_size, dtype=torch.float32)
        coords -= self.window_size // 2

        g = torch.exp(-(coords**2) / (2 * self.sigma**2))
        g /= g.sum()

        return g.outer(g).unsqueeze(0).unsqueeze(0)

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        计算预测值和目标值之间的SSIM。

        参数:
            pred: 预测图像 [B, C, H, W]。
            target: 目标图像 [B, C, H, W]。

        返回:
            SSIM值。
        """
        batch_size, channels = pred.shape[:2]

        # 如果需要，转换为灰度图
        if channels == 3:
            # RGB到灰度图转换
            weights = torch.tensor([0.299, 0.587, 0.114], device=pred.device)
            pred = (pred * weights.view(1, 3, 1, 1)).sum(dim=1, keepdim=True)
            target = (target * weights.view(1, 3, 1, 1)).sum(dim=1, keepdim=True)

        # 计算局部均值
        mu1 = F.conv2d(pred, self.kernel, padding=self.window_size // 2)
        mu2 = F.conv2d(target, self.kernel, padding=self.window_size // 2)

        mu1_sq = mu1.pow(2)
        mu2_sq = mu2.pow(2)
        mu1_mu2 = mu1 * mu2

        # 计算局部方差和协方差
        sigma1_sq = F.conv2d(pred * pred, self.kernel, padding=self.window_size // 2) - mu1_sq
        sigma2_sq = F.conv2d(target * target, self.kernel, padding=self.window_size // 2) - mu2_sq
        sigma12 = F.conv2d(pred * target, self.kernel, padding=self.window_size // 2) - mu1_mu2

        # SSIM计算
        c1 = (self.k1 * 1.0) ** 2  # Assuming max_val = 1.0
        c2 = (self.k2 * 1.0) ** 2

        numerator = (2 * mu1_mu2 + c1) * (2 * sigma12 + c2)
        denominator = (mu1_sq + mu2_sq + c1) * (sigma1_sq + sigma2_sq + c2)

        ssim_map = numerator / denominator
        return ssim_map.mean()


@register_metric("lpips")
class LPIPS(nn.Module):
    """学习感知图像块相似性。"""

    def __init__(self, network: str = "alex", spatial: bool = False):
        """
        参数:
            network: 使用的网络 ('alex', 'vgg', 'squeeze')。
            spatial: 是否返回空间维度。
        """
        super().__init__()
        # 设置verbose=False以避免LPIPS初始化时的冗余输出
        self.lpips_fn = lpips.LPIPS(net=network, spatial=spatial, verbose=False)

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        计算预测值和目标值之间的LPIPS。

        参数:
            pred: 预测图像 [B, C, H, W]，范围 [0, 1]。
            target: 目标图像 [B, C, H, W]，范围 [0, 1]。

        返回:
            LPIPS值。
        """
        # LPIPS期望图像范围为[-1, 1]
        pred_norm = pred * 2.0 - 1.0
        target_norm = target * 2.0 - 1.0

        return self.lpips_fn(pred_norm, target_norm).mean()


@register_metric("mae")
class MAE(nn.Module):
    """平均绝对误差。"""

    def __init__(self, reduction: str = "mean"):
        super().__init__()
        self.reduction = reduction

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        return F.l1_loss(pred, target, reduction=self.reduction)


@register_metric("mse")
class MSE(nn.Module):
    """均方误差。"""

    def __init__(self, reduction: str = "mean"):
        super().__init__()
        self.reduction = reduction

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        return F.mse_loss(pred, target, reduction=self.reduction)


class MetricTracker:
    """在训练/验证期间跟踪和计算指标。"""

    def __init__(self, metrics: Dict[str, nn.Module]):
        """
        参数:
            metrics: 指标名称到指标实例的字典。
        """
        self.metrics = metrics
        self.reset()

    def reset(self):
        """重置所有跟踪的值。"""
        self.values = {name: [] for name in self.metrics.keys()}
        self.count = 0

    def update(self, pred: torch.Tensor, target: torch.Tensor) -> Dict[str, float]:
        """
        用新的预测值和目标值更新指标。

        参数:
            pred: 预测图像。
            target: 目标图像。

        返回:
            当前指标值的字典。
        """
        results = {}

        with torch.no_grad():
            for name, metric in self.metrics.items():
                try:
                    value = metric(pred, target)
                    if isinstance(value, torch.Tensor):
                        value = value.item()

                    self.values[name].append(value)
                    results[name] = value

                except Exception as e:
                    print(f"Warning: Failed to compute {name}: {e}")
                    results[name] = float("nan")

        self.count += 1
        return results

    def compute(self) -> Dict[str, float]:
        """
        计算所有更新的平均指标。

        返回:
            平均指标值的字典。
        """
        if self.count == 0:
            return {name: 0.0 for name in self.metrics.keys()}

        return {name: np.mean(values) if values else 0.0 for name, values in self.values.items()}

    def get_best(self, mode: str = "max") -> Dict[str, float]:
        """
        获取最佳指标值。

        参数:
            mode: 'max'表示越大越好，'min'表示越小越好。

        返回:
            最佳指标值的字典。
        """
        if self.count == 0:
            return {name: 0.0 for name in self.metrics.keys()}

        func = np.max if mode == "max" else np.min
        return {name: func(values) if values else 0.0 for name, values in self.values.items()}


def create_metric_tracker(metric_names: list, device: str = "cuda") -> MetricTracker:
    """
    使用指定的指标创建指标跟踪器。

    参数:
        metric_names: 要跟踪的指标名称列表。
        device: 放置指标的设备。

    返回:
        配置好的MetricTracker实例。
    """
    from .registry import METRICS

    metrics = {}
    for name in metric_names:
        try:
            metric = METRICS.build(name)
            if hasattr(metric, "to"):
                metric = metric.to(device)
            metrics[name] = metric
        except Exception as e:
            print(f"Warning: Failed to create metric {name}: {e}")

    return MetricTracker(metrics)



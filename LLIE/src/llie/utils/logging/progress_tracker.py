"""
进度跟踪器

负责训练和评估过程中的进度可视化和状态更新。
"""

from typing import Dict, Any, Optional
import time
from rich.console import Console
from rich.progress import Progress, TextColumn, BarColumn, TimeElapsedColumn, TimeRemainingColumn
from rich.table import Table
from rich.live import Live
from loguru import logger


class ProgressTracker:
    """
    进度跟踪器

    主要功能：
    1. 训练进度的可视化跟踪
    2. 评估进度的实时更新
    3. 性能指标的动态展示
    4. 状态变化的通知机制
    """

    def __init__(self):
        """初始化进度跟踪器"""
        self.console = Console()
        self.current_progress = None
        self.current_task = None
        self.start_time = None

    def start_training_progress(self, total_epochs: int, epoch_steps: int) -> None:
        """
        开始训练进度跟踪

        Args:
            total_epochs: 总训练轮数
            epoch_steps: 每轮的步数
        """
        self.start_time = time.time()

        # 创建进度条
        self.current_progress = Progress(
            TextColumn("[bold blue]训练进度", justify="right"),
            BarColumn(bar_width=None),
            "[progress.percentage]{task.percentage:>3.1f}%",
            "•",
            TextColumn("轮次 {task.completed}/{task.total}"),
            "•",
            TimeElapsedColumn(),
            "•",
            TimeRemainingColumn(),
            console=self.console,
        )

        # 添加训练任务
        self.current_task = self.current_progress.add_task("训练中...", total=total_epochs)

        logger.info(f"开始训练：{total_epochs} 轮，每轮 {epoch_steps} 步")

    def update_epoch_progress(
        self,
        epoch: int,
        train_metrics: Dict[str, float],
        val_metrics: Optional[Dict[str, float]] = None,
    ) -> None:
        """
        更新训练轮次进度

        Args:
            epoch: 当前轮次
            train_metrics: 训练指标
            val_metrics: 验证指标（可选）
        """
        if self.current_progress and self.current_task:
            # 更新进度条
            self.current_progress.update(self.current_task, completed=epoch + 1)

            # 记录指标信息
            metrics_info = self._format_metrics(train_metrics, val_metrics)
            logger.info(f"轮次 {epoch + 1} 完成 - {metrics_info}")

    def finish_training_progress(self) -> None:
        """结束训练进度跟踪"""
        if self.current_progress:
            self.current_progress.stop()
            self.current_progress = None
            self.current_task = None

        if self.start_time:
            total_time = time.time() - self.start_time
            logger.success(f"训练完成！总用时: {total_time:.2f} 秒")

    def start_evaluation_progress(self, total_samples: int) -> None:
        """
        开始评估进度跟踪

        Args:
            total_samples: 总样本数
        """
        self.start_time = time.time()

        # 创建评估进度条
        self.current_progress = Progress(
            TextColumn("[bold green]评估进度", justify="right"),
            BarColumn(bar_width=None),
            "[progress.percentage]{task.percentage:>3.1f}%",
            "•",
            TextColumn("样本 {task.completed}/{task.total}"),
            "•",
            TimeElapsedColumn(),
            "•",
            TimeRemainingColumn(),
            console=self.console,
        )

        # 添加评估任务
        self.current_task = self.current_progress.add_task("评估中...", total=total_samples)

        logger.info(f"开始评估：{total_samples} 个样本")

    def update_evaluation_progress(self, completed_samples: int) -> None:
        """
        更新评估进度

        Args:
            completed_samples: 已完成的样本数
        """
        if self.current_progress and self.current_task:
            self.current_progress.update(self.current_task, completed=completed_samples)

    def finish_evaluation_progress(self, final_metrics: Dict[str, float]) -> None:
        """
        结束评估进度跟踪

        Args:
            final_metrics: 最终评估指标
        """
        if self.current_progress:
            self.current_progress.stop()
            self.current_progress = None
            self.current_task = None

        if self.start_time:
            total_time = time.time() - self.start_time
            metrics_info = self._format_metrics(final_metrics)
            logger.success(f"评估完成！用时: {total_time:.2f} 秒 - {metrics_info}")

    def create_metrics_table(
        self,
        train_metrics: Dict[str, float],
        val_metrics: Optional[Dict[str, float]] = None,
        title: str = "📊 训练指标",
    ) -> Table:
        """
        创建指标展示表格

        Args:
            train_metrics: 训练指标
            val_metrics: 验证指标（可选）
            title: 表格标题

        Returns:
            指标表格
        """
        table = Table(title=title, show_header=True, header_style="bold blue")
        table.add_column("指标", style="cyan", no_wrap=True)
        table.add_column("训练", style="green", justify="right")

        if val_metrics:
            table.add_column("验证", style="yellow", justify="right")

        # 添加指标行
        all_metrics = set(train_metrics.keys())
        if val_metrics:
            all_metrics.update(val_metrics.keys())

        for metric in sorted(all_metrics):
            train_val = f"{train_metrics.get(metric, 0):.4f}" if metric in train_metrics else "N/A"

            if val_metrics:
                val_val = f"{val_metrics.get(metric, 0):.4f}" if metric in val_metrics else "N/A"
                table.add_row(metric.upper(), train_val, val_val)
            else:
                table.add_row(metric.upper(), train_val)

        return table

    def _format_metrics(
        self, train_metrics: Dict[str, float], val_metrics: Optional[Dict[str, float]] = None
    ) -> str:
        """
        格式化指标信息为字符串

        Args:
            train_metrics: 训练指标
            val_metrics: 验证指标（可选）

        Returns:
            格式化的指标字符串
        """
        parts = []

        # 添加训练指标
        for key, value in train_metrics.items():
            parts.append(f"{key.upper()}: {value:.4f}")

        # 添加验证指标
        if val_metrics:
            parts.append("|")
            for key, value in val_metrics.items():
                parts.append(f"Val_{key.upper()}: {value:.4f}")

        return " ".join(parts)

    def log_phase_start(self, phase_name: str, details: str = "") -> None:
        """
        记录阶段开始

        Args:
            phase_name: 阶段名称
            details: 详细信息
        """
        separator = "=" * 50
        logger.info(separator)
        logger.info(f"🚀 开始 {phase_name}")
        if details:
            logger.info(f"详情: {details}")
        logger.info(separator)

    def log_phase_end(self, phase_name: str, summary: str = "") -> None:
        """
        记录阶段结束

        Args:
            phase_name: 阶段名称
            summary: 总结信息
        """
        separator = "=" * 50
        logger.success(f"✅ {phase_name} 完成")
        if summary:
            logger.info(f"总结: {summary}")
        logger.info(separator)

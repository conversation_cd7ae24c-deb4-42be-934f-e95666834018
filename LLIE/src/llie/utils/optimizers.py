"""
LLIE框架的优化器注册。

本模块通过注册系统注册常见的PyTorch优化器。
"""

import torch.optim as optim
from .registry import register_optimizer


# 注册常见的PyTorch优化器
@register_optimizer("Adam")
class Adam(optim.Adam):
    """Adam优化器。"""

    pass


@register_optimizer("AdamW")
class AdamW(optim.AdamW):
    """AdamW优化器。"""

    pass


@register_optimizer("SGD")
class SGD(optim.SGD):
    """SGD优化器。"""

    pass


@register_optimizer("RMSprop")
class RMSprop(optim.RMSprop):
    """RMSprop优化器。"""

    pass


@register_optimizer("Adagrad")
class Adagrad(optim.Adagrad):
    """Adagrad优化器。"""

    pass


@register_optimizer("Adadelta")
class Adadelta(optim.Adadelta):
    """Adadelta优化器。"""

    pass


@register_optimizer("Adamax")
class Adamax(optim.Adamax):
    """Adamax优化器。"""

    pass


@register_optimizer("ASGD")
class ASGD(optim.ASGD):
    """ASGD优化器。"""

    pass


@register_optimizer("LBFGS")
class LBFGS(optim.LBFGS):
    """LBFGS优化器。"""

    pass


@register_optimizer("Rprop")
class Rprop(optim.Rprop):
    """Rprop优化器。"""

    pass


@register_optimizer("SparseAdam")
class SparseAdam(optim.SparseAdam):
    """SparseAdam优化器。"""

    pass

"""
低光图像增强的现代损失函数。

本模块提供了为低光图像增强任务优化的全面损失函数集合，
包括感知损失、结构损失和专门的LLIE损失。
"""

from typing import Dict, Any, Optional, Tuple, List
import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
from loguru import logger

try:
    import lpips

    LPIPS_AVAILABLE = True
except ImportError:
    LPIPS_AVAILABLE = False
    logger.warning("LPIPS not available. Install with: pip install lpips")

from ..utils import LOSSES, register_loss


@register_loss("CharbonnierLoss")
class CharbonnierLoss(nn.Module):
    """
    Charbonnier损失（L1的平滑近似变体）。

    L1损失的平滑近似，在零附近提供更好的梯度。
    由于对异常值的鲁棒性，常用于图像恢复任务。

    参数:
        eps: 数值稳定性的小常数。默认值: 1e-6。
        reduction: 归约方法 ('mean', 'sum', 'none')。默认值: 'mean'。

    数学公式:
        L = sqrt((x - y)^2 + eps^2) - eps

    示例:
        >>> loss_fn = CharbonnierLoss(eps=1e-3)
        >>> pred = torch.randn(4, 3, 256, 256)
        >>> target = torch.randn(4, 3, 256, 256)
        >>> loss = loss_fn(pred, target)
    """

    def __init__(self, eps: float = 1e-6, reduction: str = "mean"):
        super().__init__()
        self.eps = eps
        self.reduction = reduction

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        计算预测值和目标值之间的Charbonnier损失。

        参数:
            pred: 预测张量 [B, C, H, W]。
            target: 目标张量 [B, C, H, W]。

        返回:
            计算的损失值。
        """
        diff = pred - target
        loss = torch.sqrt(diff * diff + self.eps * self.eps)

        if self.reduction == "mean":
            return loss.mean()
        elif self.reduction == "sum":
            return loss.sum()
        else:
            return loss


class VGGPerceptualLoss(nn.Module):
    """
    基于VGG的感知损失，用于增强视觉质量。

    使用预训练的VGG特征计算增强图像和真实图像之间的感知距离。

    参数:
        feature_layers: 用于特征提取的VGG层。
        weights: 每个特征层的权重。
        normalize: 是否对输入图像进行归一化。
    """

    def __init__(
        self,
        feature_layers: List[str] = ["relu1_1", "relu2_1", "relu3_1", "relu4_1", "relu5_1"],
        weights: List[float] = [1.0, 1.0, 1.0, 1.0, 1.0],
        normalize: bool = True,
    ):
        super().__init__()

        self.feature_layers = feature_layers
        self.weights = weights
        self.normalize = normalize

        # 加载预训练的VGG16
        vgg = models.vgg16(pretrained=True).features
        self.vgg = nn.Sequential()

        # 提取指定的层
        layer_names = {
            "0": "conv1_1",
            "2": "conv1_2",
            "5": "conv2_1",
            "7": "conv2_2",
            "10": "conv3_1",
            "12": "conv3_2",
            "14": "conv3_3",
            "17": "conv4_1",
            "19": "conv4_2",
            "21": "conv4_3",
            "24": "conv5_1",
            "26": "conv5_2",
            "28": "conv5_3",
        }

        relu_layers = {
            "conv1_1": "relu1_1",
            "conv2_1": "relu2_1",
            "conv3_1": "relu3_1",
            "conv4_1": "relu4_1",
            "conv5_1": "relu5_1",
        }

        for i, layer in enumerate(vgg):
            name = layer_names.get(str(i), f"layer_{i}")
            self.vgg.add_module(name, layer)

            if name in relu_layers and relu_layers[name] in feature_layers:
                self.vgg.add_module(relu_layers[name], nn.ReLU(inplace=False))

        # 冻结VGG参数
        for param in self.vgg.parameters():
            param.requires_grad = False

        # ImageNet预训练模型的归一化
        if normalize:
            self.register_buffer("mean", torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1))
            self.register_buffer("std", torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1))

    def _normalize(self, x: torch.Tensor) -> torch.Tensor:
        """为VGG标准化输入。"""
        if self.normalize:
            return (x - self.mean) / self.std
        return x

    def _extract_features(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """提取VGG特征。"""
        features = {}
        x = self._normalize(x)

        for name, module in self.vgg.named_children():
            x = module(x)
            if name in self.feature_layers:
                features[name] = x

        return features

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        计算感知损失。

        参数:
            pred: 预测图像 [B, 3, H, W]。
            target: 真实图像 [B, 3, H, W]。

        返回:
            感知损失值。
        """
        pred_features = self._extract_features(pred)
        target_features = self._extract_features(target)

        loss = 0.0
        for i, layer in enumerate(self.feature_layers):
            if layer in pred_features and layer in target_features:
                weight = self.weights[i] if i < len(self.weights) else 1.0
                loss += weight * F.mse_loss(pred_features[layer], target_features[layer])

        return loss


@register_loss("PerceptualLoss")
class PerceptualLoss(nn.Module):
    """
    统一的感知损失，支持VGG和LPIPS。

    参数:
        loss_type: 感知损失类型 ('vgg', 'lpips')。
        weight: 感知损失的权重。
    """

    def __init__(self, loss_type: str = "vgg", weight: float = 1.0):
        super().__init__()

        self.loss_type = loss_type
        self.weight = weight

        if loss_type == "vgg":
            self.perceptual_net = VGGPerceptualLoss()
        elif loss_type == "lpips" and LPIPS_AVAILABLE:
            self.perceptual_net = lpips.LPIPS(net="alex", verbose=False)
        else:
            raise ValueError(f"Unsupported perceptual loss type: {loss_type}")

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """计算感知损失。"""
        return self.weight * self.perceptual_net(pred, target).mean()


@register_loss("SSIMLoss")
class SSIMLoss(nn.Module):
    """
    结构相似性指数(SSIM)损失。

    参数:
        window_size: 高斯窗口的大小。
        sigma: 高斯窗口的标准差。
        reduction: 归约方法 ('mean', 'sum', 'none')。
    """

    def __init__(self, window_size: int = 11, sigma: float = 1.5, reduction: str = "mean"):
        super().__init__()

        self.window_size = window_size
        self.sigma = sigma
        self.reduction = reduction

        # 创建高斯窗口
        self.register_buffer("window", self._create_window(window_size, sigma))

    def _gaussian(self, window_size: int, sigma: float) -> torch.Tensor:
        """创建一维高斯核。"""
        gauss = torch.exp(
            -torch.arange(window_size, dtype=torch.float).sub(window_size // 2).pow(2)
            / (2 * sigma**2)
        )
        return gauss / gauss.sum()

    def _create_window(self, window_size: int, sigma: float) -> torch.Tensor:
        """创建二维高斯窗口。"""
        _1D_window = self._gaussian(window_size, sigma).unsqueeze(1)
        _2D_window = _1D_window.mm(_1D_window.t()).float().unsqueeze(0).unsqueeze(0)
        return _2D_window

    def _ssim(self, img1: torch.Tensor, img2: torch.Tensor) -> torch.Tensor:
        """计算两幅图像之间的SSIM。"""
        C1 = 0.01**2
        C2 = 0.03**2

        mu1 = F.conv2d(img1, self.window, padding=self.window_size // 2, groups=img1.shape[1])
        mu2 = F.conv2d(img2, self.window, padding=self.window_size // 2, groups=img2.shape[1])

        mu1_sq = mu1.pow(2)
        mu2_sq = mu2.pow(2)
        mu1_mu2 = mu1 * mu2

        sigma1_sq = (
            F.conv2d(img1 * img1, self.window, padding=self.window_size // 2, groups=img1.shape[1])
            - mu1_sq
        )
        sigma2_sq = (
            F.conv2d(img2 * img2, self.window, padding=self.window_size // 2, groups=img2.shape[1])
            - mu2_sq
        )
        sigma12 = (
            F.conv2d(img1 * img2, self.window, padding=self.window_size // 2, groups=img1.shape[1])
            - mu1_mu2
        )

        ssim_map = ((2 * mu1_mu2 + C1) * (2 * sigma12 + C2)) / (
            (mu1_sq + mu2_sq + C1) * (sigma1_sq + sigma2_sq + C2)
        )

        return ssim_map

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        计算SSIM损失。

        参数:
            pred: 预测图像 [B, C, H, W]。
            target: 真实图像 [B, C, H, W]。

        返回:
            SSIM损失 (1 - SSIM)。
        """
        # Expand window for multi-channel images
        if self.window.shape[1] != pred.shape[1]:
            window = self.window.expand(pred.shape[1], 1, self.window_size, self.window_size)
            self.register_buffer("window", window)

        ssim_val = self._ssim(pred, target)
        ssim_loss = 1 - ssim_val

        if self.reduction == "mean":
            return ssim_loss.mean()
        elif self.reduction == "sum":
            return ssim_loss.sum()
        else:
            return ssim_loss


@register_loss("EdgeLoss")
class EdgeLoss(nn.Module):
    """
    使用Sobel算子的边缘保持损失。

    有助于保持增强图像中的精细细节和边缘。

    参数:
        weight: 边缘损失的权重。
    """

    def __init__(self, weight: float = 1.0):
        super().__init__()

        self.weight = weight

        # Sobel算子
        sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=torch.float32)
        sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=torch.float32)

        self.register_buffer("sobel_x", sobel_x.view(1, 1, 3, 3))
        self.register_buffer("sobel_y", sobel_y.view(1, 1, 3, 3))

    def _get_edges(self, img: torch.Tensor) -> torch.Tensor:
        """使用Sobel算子提取边缘。"""
        B, C, H, W = img.shape
        img_gray = img.mean(dim=1, keepdim=True)  # Convert to grayscale

        edges_x = F.conv2d(img_gray, self.sobel_x, padding=1)
        edges_y = F.conv2d(img_gray, self.sobel_y, padding=1)

        edges = torch.sqrt(edges_x**2 + edges_y**2)
        return edges

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        计算边缘损失。

        参数:
            pred: 预测图像 [B, C, H, W]。
            target: 真实图像 [B, C, H, W]。

        返回:
            边缘保持损失。
        """
        pred_edges = self._get_edges(pred)
        target_edges = self._get_edges(target)

        return self.weight * F.l1_loss(pred_edges, target_edges)


@register_loss("ColorLoss")
class ColorLoss(nn.Module):
    """
    LAB色彩空间中的颜色一致性损失。

    通过在感知均匀的LAB色彩空间中比较，确保增强图像的色彩保真度。

    参数:
        weight: 颜色损失的权重。
    """

    def __init__(self, weight: float = 1.0):
        super().__init__()
        self.weight = weight

    def rgb_to_lab(self, rgb: torch.Tensor) -> torch.Tensor:
        """
        将RGB转换为LAB色彩空间（近似值）。

        参数:
            rgb: RGB图像 [B, 3, H, W]，范围 [0, 1]。

        返回:
            LAB图像 [B, 3, H, W]。
        """
        # 简化的RGB到LAB转换
        # 这是出于效率考虑的近似值
        r, g, b = rgb[:, 0:1], rgb[:, 1:2], rgb[:, 2:3]

        # 转换为线性RGB
        r = torch.where(r > 0.04045, ((r + 0.055) / 1.055) ** 2.4, r / 12.92)
        g = torch.where(g > 0.04045, ((g + 0.055) / 1.055) ** 2.4, g / 12.92)
        b = torch.where(b > 0.04045, ((b + 0.055) / 1.055) ** 2.4, b / 12.92)

        # 转换为XYZ
        x = r * 0.4124 + g * 0.3576 + b * 0.1805
        y = r * 0.2126 + g * 0.7152 + b * 0.0722
        z = r * 0.0193 + g * 0.1192 + b * 0.9505

        # 转换为LAB
        x = x / 0.95047
        y = y / 1.00000
        z = z / 1.08883

        x = torch.where(x > 0.008856, x ** (1 / 3), (7.787 * x) + (16 / 116))
        y = torch.where(y > 0.008856, y ** (1 / 3), (7.787 * y) + (16 / 116))
        z = torch.where(z > 0.008856, z ** (1 / 3), (7.787 * z) + (16 / 116))

        L = (116 * y) - 16
        A = 500 * (x - y)
        B = 200 * (y - z)

        return torch.cat([L, A, B], dim=1)

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        计算颜色一致性损失。

        参数:
            pred: 预测图像 [B, C, H, W]。
            target: 真实图像 [B, C, H, W]。

        返回:
            颜色一致性损失。
        """
        pred_lab = self.rgb_to_lab(pred)
        target_lab = self.rgb_to_lab(target)

        return self.weight * F.l1_loss(pred_lab, target_lab)


@register_loss("CombinedLoss")
class CombinedLoss(nn.Module):
    """
    综合LLIE训练的组合损失函数。

    结合多个损失组件进行平衡优化：
    - 像素级损失 (L1/L2)
    - 感知损失 (VGG/LPIPS)
    - 结构损失 (SSIM)
    - 边缘保持损失
    - 颜色一致性损失

    参数:
        pixel_weight: 像素级损失的权重。
        perceptual_weight: 感知损失的权重。
        ssim_weight: SSIM损失的权重。
        edge_weight: 边缘损失的权重。
        color_weight: 颜色损失的权重。
        pixel_loss_type: 像素损失类型 ('l1', 'l2', 'smooth_l1')。
        perceptual_type: 感知损失类型 ('vgg', 'lpips')。
    """

    def __init__(
        self,
        pixel_weight: float = 1.0,
        perceptual_weight: float = 0.1,
        ssim_weight: float = 0.1,
        edge_weight: float = 0.05,
        color_weight: float = 0.05,
        pixel_loss_type: str = "l1",
        perceptual_type: str = "vgg",
    ):
        super().__init__()

        self.pixel_weight = pixel_weight
        self.perceptual_weight = perceptual_weight
        self.ssim_weight = ssim_weight
        self.edge_weight = edge_weight
        self.color_weight = color_weight

        # 像素级损失
        if pixel_loss_type == "l1":
            self.pixel_loss = nn.L1Loss()
        elif pixel_loss_type == "l2":
            self.pixel_loss = nn.MSELoss()
        elif pixel_loss_type == "smooth_l1":
            self.pixel_loss = nn.SmoothL1Loss()
        else:
            raise ValueError(f"Unsupported pixel loss type: {pixel_loss_type}")

        # 组件损失
        if perceptual_weight > 0:
            self.perceptual_loss = PerceptualLoss(perceptual_type, weight=1.0)

        if ssim_weight > 0:
            self.ssim_loss = SSIMLoss()

        if edge_weight > 0:
            self.edge_loss = EdgeLoss(weight=1.0)

        if color_weight > 0:
            self.color_loss = ColorLoss(weight=1.0)

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        计算组合损失。

        参数:
            pred: 预测图像 [B, C, H, W]。
            target: 真实图像 [B, C, H, W]。

        返回:
            包含各个损失和总损失的字典。
        """
        losses = {}

        # 像素级损失
        losses["pixel"] = self.pixel_loss(pred, target)
        total_loss = self.pixel_weight * losses["pixel"]

        # 感知损失
        if self.perceptual_weight > 0:
            losses["perceptual"] = self.perceptual_loss(pred, target)
            total_loss += self.perceptual_weight * losses["perceptual"]

        # SSIM损失
        if self.ssim_weight > 0:
            losses["ssim"] = self.ssim_loss(pred, target)
            total_loss += self.ssim_weight * losses["ssim"]

        # 边缘损失
        if self.edge_weight > 0:
            losses["edge"] = self.edge_loss(pred, target)
            total_loss += self.edge_weight * losses["edge"]

        # 颜色损失
        if self.color_weight > 0:
            losses["color"] = self.color_loss(pred, target)
            total_loss += self.color_weight * losses["color"]

        losses["total"] = total_loss
        return losses


# 从配置构建损失函数的工具函数
def build_loss(config: Dict[str, Any]) -> nn.Module:
    """
    从配置字典构建损失函数。

    参数:
        config: 包含'type'和其他参数的损失配置。

    返回:
        初始化的损失函数。

    示例:
        >>> config = {
        ...     'type': 'CombinedLoss',
        ...     'pixel_weight': 1.0,
        ...     'perceptual_weight': 0.1,
        ...     'ssim_weight': 0.1
        ... }
        >>> loss_fn = build_loss(config)
    """
    from ...utils.registry import LOSSES

    loss_type = config.pop("type")
    return LOSSES.build(loss_type, **config)

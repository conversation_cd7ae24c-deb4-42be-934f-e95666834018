"""
现代化训练引擎

本模块实现了一个功能完整的深度学习训练引擎，具有以下特性：
- Hydra配置管理：灵活的配置系统
- W&B实验跟踪：自动记录训练过程和结果
- Rich进度显示：美观的训练进度条和状态显示
- 混合精度训练：加速训练并节省显存
- 模型检查点：自动保存和恢复训练状态
- 早停机制：防止过拟合
- 指数移动平均：提高模型稳定性
- 梯度裁剪：防止梯度爆炸
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional, Union
import time

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import wandb
from tqdm.rich import tqdm
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, TextColumn, BarColumn, TimeElapsedColumn
from loguru import logger

from ..utils.registry import MODELS, LOSSES, OPTIMIZERS, SCHEDULERS
from ..utils.metrics import create_metric_tracker
from ..models.base_model import ModelEMA, GradientClipping
from ..utils.logging import ExperimentLogger
from ..utils.experiment_manager import ExperimentManager


class ModernTrainer:
    """
    现代化训练器 - 具有全面日志记录和实验跟踪功能

    主要特性：
    - Hydra配置管理：灵活的层次化配置系统
    - W&B实验跟踪：自动记录训练指标、模型参数和可视化结果
    - Rich进度条：美观的训练进度显示和状态监控
    - 自动检查点：智能保存和恢复训练状态
    - 梯度裁剪和EMA：提高训练稳定性和模型性能
    - 混合精度训练：支持FP16加速训练并节省显存
    - 早停机制：基于验证指标的智能早停
    - 多指标监控：支持PSNR、SSIM、MAE等多种评估指标

    使用方法：
        trainer = ModernTrainer(config)
        trainer.train(train_loader, val_loader)
    """

    def __init__(self, config: Dict[str, Any]):
        """
        使用配置初始化训练器

        参数:
            config: Hydra配置字典，包含所有训练相关的参数
        """
        self.config = config
        self.console = Console()  # Rich控制台，用于美观的输出显示

        # 设置计算设备（GPU/CPU）
        self.device = torch.device(config.get("device", "cuda"))
        logger.info(f"使用计算设备: {self.device}")

        # 设置输出目录 - 直接使用Hydra提供的任务分离路径
        try:
            from hydra.core.hydra_config import HydraConfig

            hydra_cfg = HydraConfig.get()
            self.output_dir = Path(hydra_cfg.runtime.output_dir)
            logger.info(f"使用Hydra训练输出目录: {self.output_dir}")
        except:
            # 如果Hydra不可用，使用路径管理器创建目录
            from ..utils.storage import OutputManager

            base_output_dir = Path(config.get("output_dir", "./outputs"))
            output_manager = OutputManager(base_output_dir, task_type="train")
            self.output_dir = output_manager.create_experiment_directory("train")
            logger.warning("Hydra配置不可用，使用路径管理器创建训练目录")

        # 创建分类子目录结构
        self.models_dir = self.output_dir / "models"
        self.evaluation_dir = self.output_dir / "evaluation"
        self.logs_dir = self.output_dir / "logs"
        self.config_dir = self.output_dir / "config"

        # 创建所有目录
        for dir_path in [
            self.output_dir,
            self.models_dir,
            self.evaluation_dir,
            self.logs_dir,
            self.config_dir,
        ]:
            dir_path.mkdir(parents=True, exist_ok=True)

        # 设置文件日志记录
        log_file = self.logs_dir / "run.log"
        from loguru import logger as loguru_logger

        loguru_logger.add(
            str(log_file),
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level="INFO",
            rotation="10 MB",
            retention="30 days",
        )

        logger.info(f"输出目录: {self.output_dir}")
        logger.info(f"  - 模型文件: {self.models_dir}")
        logger.info(f"  - 评估结果: {self.evaluation_dir}")
        logger.info(f"  - 训练日志: {self.logs_dir}")
        logger.info(f"  - 配置文件: {self.config_dir}")
        logger.info(f"日志文件: {log_file}")

        # 初始化实验管理器和日志系统
        self.experiment_manager = ExperimentManager()
        self.experiment_logger = None  # 将在train方法中初始化

        # 初始化各个组件
        self._setup_model()  # 设置模型
        self._setup_loss()  # 设置损失函数
        self._setup_optimizer()  # 设置优化器
        self._setup_scheduler()  # 设置学习率调度器
        self._setup_metrics()  # 设置评估指标
        self._setup_training_utilities()  # 设置训练工具（EMA、梯度裁剪等）

        # 训练状态变量
        self.current_epoch = 0  # 当前训练轮数
        self.current_step = 0  # 当前训练步数
        self.best_metrics = {}  # 最佳指标记录

        logger.info("训练器初始化完成")

    def _setup_model(self):
        """
        从配置中设置模型

        该方法负责：
        1. 解析模型配置
        2. 从注册表中构建模型
        3. 将模型移动到指定设备
        4. 记录模型信息
        """
        from omegaconf import OmegaConf

        # 处理字典和OmegaConf配置格式
        if isinstance(self.config["model"], dict):
            model_config = self.config["model"].copy()
        else:
            model_config = OmegaConf.to_container(self.config["model"], resolve=True)

        model_type = model_config.get("type", "Unknown")
        # 从模型注册表中构建模型
        self.model = MODELS.build(model_config.copy())
        # 将模型移动到指定设备（GPU/CPU）
        self.model = self.model.to(self.device)

        # Multi-GPU support
        if torch.cuda.device_count() > 1:
            logger.info(f"Using {torch.cuda.device_count()} GPUs")
            self.model = nn.DataParallel(self.model)

        # Log model info
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)

        logger.info(f"Model: {model_type}")
        logger.info(f"Total parameters: {total_params:,}")
        logger.info(f"Trainable parameters: {trainable_params:,}")

        # Log to W&B
        if wandb.run is not None:
            wandb.log(
                {
                    "model/total_params": total_params,
                    "model/trainable_params": trainable_params,
                }
            )

    def _setup_loss(self):
        """Setup loss function from configuration."""
        from omegaconf import OmegaConf

        # Handle both dict and OmegaConf config
        if isinstance(self.config["trainer"]["loss"], dict):
            loss_config = self.config["trainer"]["loss"].copy()
        else:
            loss_config = OmegaConf.to_container(self.config["trainer"]["loss"], resolve=True)

        loss_type = loss_config.get("type", "Unknown")
        self.criterion = LOSSES.build(loss_config)
        self.criterion = self.criterion.to(self.device)

        logger.info(f"Loss function: {loss_type}")

    def _setup_optimizer(self):
        """Setup optimizer from configuration."""
        from omegaconf import OmegaConf

        # Handle both dict and OmegaConf config
        if isinstance(self.config["trainer"]["optimizer"], dict):
            optim_config = self.config["trainer"]["optimizer"].copy()
            optim_type = optim_config.pop("type")
        else:
            optim_config = OmegaConf.to_container(self.config["trainer"]["optimizer"], resolve=True)
            optim_type = optim_config.pop("type")

        # Add model parameters
        optim_config["params"] = self.model.parameters()

        self.optimizer = OPTIMIZERS.build(optim_type, **optim_config)

        logger.info(f"Optimizer: {optim_type}")
        logger.info(f"Learning rate: {optim_config.get('lr', 'N/A')}")

    def _setup_scheduler(self):
        """Setup learning rate scheduler from configuration."""
        if "scheduler" in self.config["trainer"]:
            from omegaconf import OmegaConf

            # Handle both dict and OmegaConf config
            if isinstance(self.config["trainer"]["scheduler"], dict):
                sched_config = self.config["trainer"]["scheduler"].copy()
                sched_type = sched_config.pop("type")
            else:
                sched_config = OmegaConf.to_container(
                    self.config["trainer"]["scheduler"], resolve=True
                )
                sched_type = sched_config.pop("type")

            # Add optimizer
            sched_config["optimizer"] = self.optimizer

            self.scheduler = SCHEDULERS.build(sched_type, **sched_config)
            logger.info(f"Scheduler: {sched_type}")
        else:
            self.scheduler = None

    def _setup_metrics(self):
        """Setup metrics from configuration."""
        metric_names = self.config["trainer"].get("metrics", ["psnr", "ssim"])
        self.train_metrics = create_metric_tracker(metric_names, self.device)
        self.val_metrics = create_metric_tracker(metric_names, self.device)

        logger.info(f"Metrics: {metric_names}")

    def _setup_training_utilities(self):
        """Setup training utilities like EMA and gradient clipping."""
        trainer_config = self.config["trainer"]

        # Exponential Moving Average
        if trainer_config.get("use_ema", False):
            ema_decay = trainer_config.get("ema_decay", 0.9999)
            self.ema = ModelEMA(self.model, decay=ema_decay)
            logger.info(f"EMA enabled with decay: {ema_decay}")
        else:
            self.ema = None

        # Gradient clipping
        if trainer_config.get("gradient_clip_norm", 0) > 0:
            max_norm = trainer_config["gradient_clip_norm"]
            self.grad_clipper = GradientClipping(max_norm=max_norm)
            logger.info(f"Gradient clipping enabled with max norm: {max_norm}")
        else:
            self.grad_clipper = None

        # Mixed precision training
        self.use_amp = trainer_config.get("use_amp", False)
        if self.use_amp:
            self.scaler = torch.cuda.amp.GradScaler()
            logger.info("Mixed precision training enabled")

    def train_epoch(self, train_loader: DataLoader, epoch: int) -> Dict[str, float]:
        """
        Train for one epoch.

        Args:
            train_loader: Training data loader.
            epoch: Current epoch number.

        Returns:
            Dictionary of training metrics.
        """
        self.model.train()
        self.train_metrics.reset()

        # Progress bar
        pbar = tqdm(train_loader, desc=f"Epoch {epoch:3d}", leave=False, dynamic_ncols=True)

        epoch_start_time = time.time()

        for batch_idx, batch in enumerate(pbar):
            self.current_step += 1

            # Move data to device
            inputs = batch["low_light"].to(self.device)
            targets = batch["normal_light"].to(self.device)

            # Forward pass
            self.optimizer.zero_grad()

            if self.use_amp:
                with torch.cuda.amp.autocast():
                    outputs = self.model(inputs)
                    loss = self.criterion(outputs, targets)

                # Backward pass
                self.scaler.scale(loss).backward()

                # Gradient clipping
                if self.grad_clipper is not None:
                    self.scaler.unscale_(self.optimizer)
                    grad_norm = self.grad_clipper(self.model)

                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                outputs = self.model(inputs)
                loss = self.criterion(outputs, targets)

                # Backward pass
                loss.backward()

                # Gradient clipping
                grad_norm = 0.0
                if self.grad_clipper is not None:
                    grad_norm = self.grad_clipper(self.model.parameters())

                self.optimizer.step()

            # Update EMA
            if self.ema is not None:
                self.ema.update()

            # Update metrics
            with torch.no_grad():
                if isinstance(outputs, tuple):
                    outputs = outputs[0]  # Take first output if tuple

                batch_metrics = self.train_metrics.update(outputs, targets)
                batch_metrics["loss"] = loss.item()

                if self.grad_clipper is not None:
                    batch_metrics["grad_norm"] = grad_norm

            # Update progress bar
            pbar.set_postfix({"loss": f"{loss.item():.4f}", "lr": f"{self._get_current_lr():.2e}"})

            # Log to W&B
            if wandb.run is not None and self.current_step % 10 == 0:
                log_dict = {f"train/{k}": v for k, v in batch_metrics.items()}
                log_dict["train/lr"] = self._get_current_lr()
                log_dict["train/epoch"] = epoch
                wandb.log(log_dict, step=self.current_step)

        # Epoch summary
        epoch_time = time.time() - epoch_start_time
        epoch_metrics = self.train_metrics.compute()
        epoch_metrics["epoch_time"] = epoch_time

        return epoch_metrics

    def validate(self, val_loader: DataLoader, epoch: int) -> Dict[str, float]:
        """
        Validate the model.

        Args:
            val_loader: Validation data loader.
            epoch: Current epoch number.

        Returns:
            Dictionary of validation metrics.
        """
        self.model.eval()
        self.val_metrics.reset()

        # Use EMA model if available
        if self.ema is not None:
            self.ema.apply_shadow()

        val_loss = 0.0
        num_batches = 0

        with torch.no_grad():
            pbar = tqdm(val_loader, desc="Validation", leave=False, dynamic_ncols=True)

            for batch in pbar:
                inputs = batch["low_light"].to(self.device)
                targets = batch["normal_light"].to(self.device)

                # Forward pass
                if self.use_amp:
                    with torch.cuda.amp.autocast():
                        outputs = self.model(inputs)
                        loss = self.criterion(outputs, targets)
                else:
                    outputs = self.model(inputs)
                    loss = self.criterion(outputs, targets)

                # Update metrics
                if isinstance(outputs, tuple):
                    outputs = outputs[0]  # Take first output if tuple

                self.val_metrics.update(outputs, targets)
                val_loss += loss.item()
                num_batches += 1

                pbar.set_postfix({"val_loss": f"{loss.item():.4f}"})

        # Restore original model if using EMA
        if self.ema is not None:
            self.ema.restore()

        # Compute final metrics
        val_metrics = self.val_metrics.compute()
        val_metrics["loss"] = val_loss / num_batches

        return val_metrics

    def train(
        self,
        train_loader: DataLoader,
        val_loader: Optional[DataLoader] = None,
        num_epochs: Optional[int] = None,
        resume_from: Optional[str] = None,
    ):
        """
        主训练循环，集成新的日志系统

        Args:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器（可选）
            num_epochs: 训练轮数
            resume_from: 恢复训练的检查点路径
        """
        if num_epochs is None:
            num_epochs = self.config["trainer"]["max_epochs"]

        # 初始化实验日志器
        experiment_name = self.output_dir.name
        self.experiment_logger = ExperimentLogger(experiment_name, self.output_dir)

        # 记录实验开始
        from omegaconf import OmegaConf

        config_dict = OmegaConf.to_container(self.config, resolve=True)
        self.experiment_logger.log_experiment_start(config_dict)

        # Resume from checkpoint if specified
        if resume_from is not None:
            self._load_checkpoint(resume_from)

        # 记录训练开始
        epoch_steps = len(train_loader)
        self.experiment_logger.log_training_start(num_epochs, epoch_steps)

        logger.info(f"开始训练，共 {num_epochs} 轮")

        # Training loop
        for epoch in range(self.current_epoch, num_epochs):
            self.current_epoch = epoch

            # Train
            train_metrics = self.train_epoch(train_loader, epoch)

            # Validate
            val_metrics = {}
            if val_loader is not None:
                val_metrics = self.validate(val_loader, epoch)

            # Step scheduler
            if self.scheduler is not None:
                if isinstance(self.scheduler, torch.optim.lr_scheduler.ReduceLROnPlateau):
                    metric_to_monitor = self.config["trainer"].get("monitor_metric", "loss")
                    metric_value = val_metrics.get(metric_to_monitor, train_metrics.get("loss", 0))
                    self.scheduler.step(metric_value)
                else:
                    self.scheduler.step()

            # 使用新的日志系统记录轮次结果
            self.experiment_logger.log_epoch_results(epoch, train_metrics, val_metrics)

            # 保存检查点（使用新的存储系统）
            if epoch % self.config["trainer"].get("save_freq", 10) == 0:
                checkpoint_data = self._create_checkpoint_data(epoch, train_metrics, val_metrics)
                self.experiment_manager.save_model_checkpoint(
                    self.output_dir, checkpoint_data, epoch, is_final=False
                )

            # Check for early stopping
            if self._should_early_stop(val_metrics):
                logger.info("Early stopping triggered")
                break

        logger.success("训练完成！")

        # 保存最终模型（使用新的存储系统）
        final_checkpoint_data = self._create_checkpoint_data(epoch, train_metrics, val_metrics)
        final_model_path = self.experiment_manager.save_model_checkpoint(
            self.output_dir, final_checkpoint_data, epoch, is_final=True
        )

        # 更新全局最佳模型
        self._update_global_best_models(final_model_path, val_metrics)

    def _log_epoch_results(
        self, epoch: int, train_metrics: Dict[str, float], val_metrics: Dict[str, float]
    ):
        """Log epoch results with beautiful formatting."""

        # Create results table
        table = Table(title=f"Epoch {epoch:3d} Results")
        table.add_column("Metric", style="bold")
        table.add_column("Train", style="green")
        if val_metrics:
            table.add_column("Validation", style="blue")

        # Add metrics to table
        all_metrics = set(train_metrics.keys()) | set(val_metrics.keys())
        for metric in sorted(all_metrics):
            train_val = f"{train_metrics.get(metric, 0):.4f}" if metric in train_metrics else "N/A"
            if val_metrics:
                val_val = f"{val_metrics.get(metric, 0):.4f}" if metric in val_metrics else "N/A"
                table.add_row(metric, train_val, val_val)
            else:
                table.add_row(metric, train_val)

        self.console.print(table)

        # Log to W&B
        if wandb.run is not None:
            log_dict = {}
            for k, v in train_metrics.items():
                log_dict[f"train/{k}"] = v
            for k, v in val_metrics.items():
                log_dict[f"val/{k}"] = v
            log_dict["epoch"] = epoch
            wandb.log(log_dict, step=self.current_step)

    def _create_checkpoint_data(
        self, epoch: int, train_metrics: Dict[str, float], val_metrics: Dict[str, float]
    ) -> Dict[str, Any]:
        """
        创建检查点数据字典

        Args:
            epoch: 当前轮次
            train_metrics: 训练指标
            val_metrics: 验证指标

        Returns:
            检查点数据字典
        """
        checkpoint = {
            "epoch": epoch,
            "step": self.current_step,
            "model_state_dict": self.model.state_dict(),
            "optimizer_state_dict": self.optimizer.state_dict(),
            "train_metrics": train_metrics,
            "val_metrics": val_metrics,
            "config": self.config,
            "best_metrics": self.best_metrics,
        }

        if self.scheduler is not None:
            checkpoint["scheduler_state_dict"] = self.scheduler.state_dict()

        if self.ema is not None:
            checkpoint["ema_state_dict"] = self.ema.shadow

        return checkpoint

        # Save checkpoints to models directory
        if is_final:
            path = self.models_dir / "final_model.pth"
        else:
            path = self.models_dir / f"checkpoint_epoch_{epoch:03d}.pth"

        torch.save(checkpoint, path)
        logger.info(f"Checkpoint saved: {path}")

        # Also save as latest
        latest_path = self.models_dir / "latest_checkpoint.pth"
        torch.save(checkpoint, latest_path)

    def _save_config_files(self):
        """保存配置文件到config目录"""
        try:
            import shutil
            from hydra.core.hydra_config import HydraConfig

            # 获取Hydra配置目录
            hydra_cfg = HydraConfig.get()
            hydra_output_dir = Path(hydra_cfg.runtime.output_dir)
            hydra_config_dir = hydra_output_dir / ".hydra"

            if hydra_config_dir.exists():
                # 复制所有Hydra配置文件到config目录
                for config_file in hydra_config_dir.glob("*.yaml"):
                    shutil.copy2(config_file, self.config_dir)
                    logger.info(f"Configuration saved: {self.config_dir / config_file.name}")

                # 保存完整的配置信息
                from omegaconf import OmegaConf

                full_config_path = self.config_dir / "full_config.yaml"
                with open(full_config_path, "w") as f:
                    OmegaConf.save(self.config, f)
                logger.info(f"Full configuration saved: {full_config_path}")
            else:
                logger.warning("Hydra config directory not found, skipping config file saving")

        except Exception as e:
            logger.error(f"Failed to save config files: {e}")
            import traceback

            logger.debug(traceback.format_exc())

    def _load_checkpoint(self, checkpoint_path: str):
        """Load checkpoint and resume training."""

        checkpoint = torch.load(checkpoint_path, map_location=self.device)

        self.model.load_state_dict(checkpoint["model_state_dict"])
        self.optimizer.load_state_dict(checkpoint["optimizer_state_dict"])

        if "scheduler_state_dict" in checkpoint and self.scheduler is not None:
            self.scheduler.load_state_dict(checkpoint["scheduler_state_dict"])

        if "ema_state_dict" in checkpoint and self.ema is not None:
            self.ema.shadow = checkpoint["ema_state_dict"]

        self.current_epoch = checkpoint["epoch"] + 1
        self.current_step = checkpoint["step"]
        self.best_metrics = checkpoint.get("best_metrics", {})

        logger.info(f"Resumed from checkpoint: {checkpoint_path}")
        logger.info(f"Starting from epoch {self.current_epoch}, step {self.current_step}")

    def _should_early_stop(self, val_metrics: Dict[str, float]) -> bool:
        """Check if early stopping should be triggered."""

        early_stop_config = self.config["trainer"].get("early_stopping")
        if not early_stop_config or not val_metrics:
            return False

        monitor_metric = early_stop_config["monitor"]
        patience = early_stop_config["patience"]
        mode = early_stop_config.get("mode", "min")

        if monitor_metric not in val_metrics:
            return False

        current_value = val_metrics[monitor_metric]

        # Update best metrics
        if monitor_metric not in self.best_metrics:
            self.best_metrics[monitor_metric] = current_value
            self.best_metrics[f"{monitor_metric}_epoch"] = self.current_epoch
            return False

        best_value = self.best_metrics[monitor_metric]
        best_epoch = self.best_metrics[f"{monitor_metric}_epoch"]

        # Check if current is better
        is_better = (current_value < best_value) if mode == "min" else (current_value > best_value)

        if is_better:
            self.best_metrics[monitor_metric] = current_value
            self.best_metrics[f"{monitor_metric}_epoch"] = self.current_epoch
            return False

        # Check patience
        epochs_without_improvement = self.current_epoch - best_epoch
        return epochs_without_improvement >= patience

    def _get_current_lr(self) -> float:
        """Get current learning rate."""
        return self.optimizer.param_groups[0]["lr"]

    def _update_global_best_models(self, model_path: Path, val_metrics: Dict[str, float]):
        """
        更新全局最佳模型

        Args:
            model_path: 训练完成的模型路径
            val_metrics: 验证指标
        """
        try:
            from ..utils.storage import ModelManager

            # 获取数据集名称 - 从Hydra defaults中获取
            dataset_name = "unknown"
            try:
                # 尝试从Hydra配置中获取数据集名称
                from hydra.core.hydra_config import HydraConfig
                hydra_cfg = HydraConfig.get()
                if hasattr(hydra_cfg, 'cfg') and hasattr(hydra_cfg.cfg, 'defaults'):
                    for default in hydra_cfg.cfg.defaults:
                        if isinstance(default, dict) and 'dataset' in default:
                            dataset_name = default['dataset']
                            break
                        elif isinstance(default, str) and 'LOL' in default:
                            dataset_name = default
                            break

                # 如果还是没找到，从配置中获取
                if dataset_name == "unknown":
                    dataset_config = self.config.get("dataset", {})
                    if isinstance(dataset_config, dict):
                        dataset_name = dataset_config.get("name", "unknown")
                        if isinstance(dataset_name, dict):
                            dataset_name = dataset_name.get("_target_", "unknown").split(".")[-1]
            except Exception as e:
                logger.debug(f"获取数据集名称失败: {e}")
                dataset_name = "unknown"

            # 创建全局模型管理器
            global_output_dir = self.output_dir.parent.parent.parent  # 回到outputs根目录
            model_manager = ModelManager(global_output_dir)

            # 准备模型分析数据
            model_analysis = {
                "model_path": str(model_path),
                "dataset": dataset_name,
                "epoch": self.current_epoch,
                "timestamp": str(self.output_dir.name)
            }

            # 使用正确的方法签名更新最佳模型
            updated_metrics = model_manager.update_global_best_models(
                experiment_dir=self.output_dir,
                metrics=val_metrics,
                model_analysis=model_analysis,
                dataset_name=dataset_name
            )

            # 记录更新结果
            if any(updated_metrics.values()):
                updated_list = [metric for metric, updated in updated_metrics.items() if updated]
                logger.info(f"🎉 已更新最优模型: {', '.join(updated_list)}")
            else:
                logger.info("当前训练未刷新任何最优模型记录")

            logger.info(f"全局最佳模型已更新: {dataset_name}")

        except Exception as e:
            logger.warning(f"更新全局最佳模型失败: {e}")
            import traceback
            logger.debug(traceback.format_exc())

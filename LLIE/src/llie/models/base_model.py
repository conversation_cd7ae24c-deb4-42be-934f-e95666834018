"""
LLIE框架的基础模型架构类

本模块提供了所有模型架构的基础类，包括：
- BaseArchitecture: 所有模型架构的基类
- ModelEMA: 指数移动平均模型参数管理
- GradientClipping: 梯度裁剪工具类

这些基础类为LLIE框架提供了统一的接口和通用功能。
"""

import torch
import torch.nn as nn
from typing import Dict, Any, Optional, Union


class BaseArchitecture(nn.Module):
    """
    所有模型架构的基础类

    这是LLIE框架中所有模型架构的基类，提供了通用的功能，包括：
    - 配置管理
    - 参数计数
    - 标准化接口
    - 序列化支持

    所有自定义模型都应该继承此类以确保框架的一致性。

    属性:
        无公共属性，但提供以下方法：
        - forward(): 前向传播（必须被子类实现）
        - get_config(): 获取模型配置
        - num_parameters: 总参数数量
        - num_trainable_parameters: 可训练参数数量
    """

    def __init__(self):
        """
        初始化基础架构

        调用父类nn.Module的初始化方法，为所有模型提供PyTorch模块的基础功能。
        """
        super().__init__()

    def forward(self, *args, **kwargs):
        """
        前向传播实现

        这是抽象方法，必须被子类实现以定义具体的模型前向传播逻辑。

        参数:
            *args: 位置参数
            **kwargs: 关键字参数

        抛出:
            NotImplementedError: 如果子类没有实现此方法
        """
        raise NotImplementedError("子类必须实现forward()方法")

    def get_config(self) -> Dict[str, Any]:
        """
        获取模型配置用于序列化

        返回模型的配置字典，用于模型保存和加载。
        子类应该重写此方法以包含模型特定的配置信息。

        返回:
            包含模型类型的基本配置字典
        """
        return {"type": self.__class__.__name__}

    @property
    def num_parameters(self) -> int:
        """获取模型总参数数量"""
        return sum(p.numel() for p in self.parameters())

    @property
    def num_trainable_parameters(self) -> int:
        """获取模型可训练参数数量"""
        return sum(p.numel() for p in self.parameters() if p.requires_grad)


class ModelEMA:
    """
    模型参数的指数移动平均（Exponential Moving Average）

    EMA是一种常用的训练技巧，通过维护模型参数的移动平均值来提高模型的泛化能力。
    EMA模型通常在验证和测试时使用，因为它具有更好的稳定性。

    工作原理:
    - EMA参数 = decay * EMA参数 + (1 - decay) * 当前参数
    - decay值接近1时，EMA参数变化更缓慢，更稳定

    参数:
        model: 要应用EMA的原始模型
        decay: EMA衰减率，通常设置为0.999或0.9999
        device: EMA模型所在的设备，默认与原始模型相同

    使用示例:
        >>> model = MyModel()
        >>> ema = ModelEMA(model, decay=0.999)
        >>> # 训练过程中更新EMA
        >>> ema.update()
        >>> # 验证时应用EMA参数
        >>> ema.apply_shadow()
        >>> # 验证完成后恢复原始参数
        >>> ema.restore()
    """

    def __init__(self, model: nn.Module, decay: float = 0.9999, device: Optional[str] = None):
        """
        初始化EMA模型

        参数:
            model: 要应用EMA的原始模型
            decay: EMA衰减率，控制新旧参数的权重
            device: EMA模型所在的设备
        """
        self.model = model
        self.decay = decay
        self.device = device if device is not None else next(model.parameters()).device

        # 创建EMA模型副本
        # 如果模型有config属性，使用config创建新实例
        self.ema_model = type(model)(model.config) if hasattr(model, "config") else model
        self.ema_model.to(self.device)
        self.ema_model.eval()  # EMA模型始终处于评估模式

        # 初始化EMA参数
        # 将原始模型参数复制到EMA模型
        for ema_param, model_param in zip(self.ema_model.parameters(), model.parameters()):
            ema_param.data.copy_(model_param.data)

    def update(self, model: Optional[nn.Module] = None):
        """
        更新EMA参数

        根据当前模型参数更新EMA参数。如果在训练过程中模型发生了变化，
        可以传入新的模型实例。

        参数:
            model: 可选的新模型实例
        """
        if model is not None:
            self.model = model

        with torch.no_grad():
            # 对每个参数应用EMA更新公式
            # ema_param = decay * ema_param + (1 - decay) * model_param
            for ema_param, model_param in zip(self.ema_model.parameters(), self.model.parameters()):
                ema_param.data.mul_(self.decay).add_(model_param.data, alpha=1 - self.decay)

    def apply_shadow(self):
        """
        应用EMA参数到原始模型用于验证

        在验证或测试前调用此方法，将EMA参数应用到原始模型上。
        这样可以使用更稳定的EMA参数进行推理，同时保持原始模型的训练状态。
        """
        # 保存原始参数
        self._backup = {}
        for name, param in self.model.named_parameters():
            self._backup[name] = param.data.clone()

        # 应用EMA参数到原始模型
        for (name, param), ema_param in zip(
            self.model.named_parameters(), self.ema_model.parameters()
        ):
            param.data.copy_(ema_param.data)

    def restore(self):
        """
        验证完成后恢复原始模型参数

        在验证或测试完成后调用此方法，恢复原始模型的参数，
        以便继续训练。
        """
        if hasattr(self, "_backup"):
            for name, param in self.model.named_parameters():
                if name in self._backup:
                    param.data.copy_(self._backup[name])
            del self._backup

    @property
    def shadow(self):
        """
        获取EMA模型的状态字典用于保存检查点

        返回:
            EMA模型的状态字典，可以直接用于torch.save()
        """
        return self.ema_model.state_dict()


class GradientClipping:
    """
    梯度裁剪工具类

    梯度裁剪是一种防止梯度爆炸的技术，通过限制梯度的范数来稳定训练过程。
    常用于深度学习和大规模模型的训练中。

    工作原理:
    - 计算梯度的范数
    - 如果范数超过阈值，按比例缩小所有梯度
    - 保持梯度的方向不变，只改变大小

    参数:
        max_norm: 梯度的最大范数，默认为1.0
        norm_type: 范数类型，2.0表示L2范数，1.0表示L1范数，默认为2.0

    使用示例:
        >>> clipper = GradientClipping(max_norm=1.0, norm_type=2.0)
        >>> # 在优化器step之前调用
        >>> clipper(model.parameters())
    """

    def __init__(self, max_norm: float = 1.0, norm_type: float = 2.0):
        """
        初始化梯度裁剪器

        参数:
            max_norm: 梯度的最大允许范数
            norm_type: 用于计算范数的类型（1.0=L1, 2.0=L2）
        """
        self.max_norm = max_norm
        self.norm_type = norm_type

    def __call__(self, parameters):
        """
        应用梯度裁剪

        对给定的参数列表应用梯度裁剪。如果传入的是单个张量，
        会自动转换为列表格式。

        参数:
            parameters: 模型参数的迭代器或单个张量

        返回:
            裁剪后的梯度范数（标量张量）
        """
        if isinstance(parameters, torch.Tensor):
            parameters = [parameters]
        return torch.nn.utils.clip_grad_norm_(parameters, self.max_norm, self.norm_type)

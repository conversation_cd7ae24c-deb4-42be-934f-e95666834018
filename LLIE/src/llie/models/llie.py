"""
LLIE: 低光图像增强模型

本模块实现了完整的LLIE架构，用于低光图像增强。该模型采用单模态处理，
包含四个处理级别：

1. 亮度图处理 (Luminance Map Processing)
   - 提取图像的亮度信息
   - 为后续处理提供照明指导

2. 傅里叶域增强 (Fourier Domain Enhancement)
   - 在频域进行全局特征处理
   - 利用FFT进行亮度特征融合

3. 空间-频率双重处理 (Spatial-Frequency Dual Processing)
   - 结合空间域和频域特征
   - 使用FFC块进行特征融合

4. 最终重建 (Final Reconstruction)
   - 生成增强后的图像
   - 保持图像细节和颜色一致性

模型特点：
- 基于亮度指导，实现高效的低光增强
- 多尺度特征处理，适应不同光照条件
- 端到端训练，无需复杂的预处理步骤
"""

from typing import Tuple, Dict, Any, Optional
import torch
import torch.nn as nn
import torch.nn.functional as F
from loguru import logger

from ..utils.registry import register_model
from .base_model import BaseArchitecture
from .components.luminance_map import LuminanceMapProcessor
from .components.fourier_blocks import FirstProcessModel
from .components.ffc_blocks import SecondProcessModel


@register_model("LLIE")
class LLIE(BaseArchitecture):
    """
    LLIE: 低光图像增强网络

    这是协调四级增强过程的主要模型：
    1. 亮度图生成 - 为照明提供指导信息
    2. 傅里叶域处理 - 进行频率域增强
    3. 双分支空间-频率处理 - 融合空间和频域特征
    4. 最终重建 - 使用残差连接进行图像重建

    模型接受可见光图像输入，内部生成亮度信息，
    最终输出增强的低光图像。

    参数说明:
        y_channels: 亮度处理通道数 (默认: 16)
        f_channels: 傅里叶处理通道数 (默认: 16)
        s_channels: 空间处理通道数 (默认: 32)
        input_channels: 输入图像通道数 (默认: 3, RGB图像)
        luminance_depth: 亮度处理器的深度配置
        fourier_blocks: 傅里叶处理块数量 (默认: 6)
        spatial_blocks: 空间处理块数量 (默认: 6)

    网络架构特点:
        - 多尺度特征提取和融合
        - 频域和空间域的协同处理
        - 残差连接保持细节信息
        - 端到端可训练的架构
    """

    def __init__(
        self,
        architecture: Optional[Dict[str, Any]] = None,
        components: Optional[Dict[str, Any]] = None,
        pretrained: Optional[str] = None,
        init: Optional[Dict[str, Any]] = None,
        **kwargs,
    ):
        """
        初始化LLIE模型

        参数:
            architecture: 架构配置字典，包含通道数和块数等参数
            components: 组件配置字典，包含各个子模块的具体配置
            pretrained: 预训练模型路径（可选）
            init: 初始化配置字典
            **kwargs: 其他参数
        """
        super().__init__()

        # 提取架构参数，设置默认值
        arch_config = architecture or {}
        self.y_channels = arch_config.get("y_nf", 16)  # 亮度处理通道数
        self.f_channels = arch_config.get("f_nf", 16)  # 傅里叶处理通道数
        self.s_channels = arch_config.get("s_nf", 32)  # 空间处理通道数
        self.input_channels = arch_config.get("input_channels", 3)  # 输入通道数(RGB)
        self.num_blocks = arch_config.get("num_blocks", 6)  # 处理块数量

        # 提取组件配置
        comp_config = components or {}
        luminance_config = comp_config.get("luminance_map", {})
        fourier_config = comp_config.get("fourier_stage", {})
        multi_config = comp_config.get("multi_stage", {})

        # 第一阶段：亮度图处理 - 为照明提供指导信息
        # 亮度图处理器用于提取和处理图像的亮度信息
        luminance_depth = luminance_config.get("depth", [1, 1, 1, 1])
        self.luminance_processor = LuminanceMapProcessor(
            depth=luminance_depth,
            base_channels=self.y_channels,
            input_channels=1,  # 亮度图是单通道
            output_channels=1,
        )

        # 第二阶段：傅里叶域处理 - 进行频率域增强
        # 在频域进行全局特征处理，捕获长距离依赖关系
        self.fourier_processor = FirstProcessModel(
            channels=self.f_channels, num_blocks=self.num_blocks, input_channels=self.input_channels
        )

        # 第三阶段：空间-频率双重处理
        # 结合空间域和频域特征，进行最终的特征融合
        self.spatial_processor = SecondProcessModel(
            channels=self.s_channels, num_blocks=self.num_blocks, input_channels=self.input_channels
        )

        # 特征适配层 - 用于亮度特征整合
        # 将亮度特征映射到统一的特征空间
        self.conv_first_map = nn.Conv2d(1, self.f_channels, 1, bias=True)  # 亮度图特征适配

        # 激活函数 - 使用LeakyReLU避免死神经元问题
        self.activation = nn.LeakyReLU(0.1, inplace=True)

        # 初始化网络权重
        self._initialize_weights()

    def _pad_to_multiple(self, x: torch.Tensor, multiple: int = 8) -> torch.Tensor:
        """
        将张量填充到指定倍数的尺寸

        这个方法确保输入张量的高度和宽度都是指定倍数的整数倍，
        这对于某些网络层（如池化层）的正确工作是必要的。

        参数:
            x: 输入张量 [B, C, H, W]
            multiple: 填充的倍数 (默认: 8)

        返回:
            填充后的张量，尺寸可被multiple整除
        """
        _, _, H, W = x.shape
        # 计算需要填充的像素数
        pad_h = (multiple - H % multiple) % multiple
        pad_w = (multiple - W % multiple) % multiple

        # 如果需要填充，使用反射填充模式
        if pad_h != 0 or pad_w != 0:
            x = F.pad(x, (0, pad_w, 0, pad_h), mode="reflect")

        return x

    def _pad_to_power_of_two(self, x: torch.Tensor) -> Tuple[torch.Tensor, Tuple[int, int]]:
        """
        将张量填充到2的幂次尺寸，用于cuFFT半精度计算

        cuFFT在半精度模式下要求输入尺寸为2的幂次。此方法将输入张量
        填充到满足此要求的最小尺寸。

        参数:
            x: 输入张量 [B, C, H, W]

        返回:
            元组 (填充后的张量, (原始高度, 原始宽度))
        """
        _, _, H, W = x.shape

        # 计算下一个2的幂次
        def next_power_of_two(n):
            return 1 << (n - 1).bit_length()

        # 计算目标尺寸
        target_H = next_power_of_two(H)
        target_W = next_power_of_two(W)

        # 计算需要填充的像素数
        pad_h = target_H - H
        pad_w = target_W - W

        # 使用反射填充模式进行填充
        if pad_h > 0 or pad_w > 0:
            # F.pad的参数顺序是 (left, right, top, bottom)
            x_padded = F.pad(x, (0, pad_w, 0, pad_h), mode="reflect")
        else:
            x_padded = x

        return x_padded, (H, W)

    def _get_amplitude_enhancement(
        self, x: torch.Tensor, luminance_map: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        使用傅里叶域处理进行幅度增强

        该方法在频域中整合亮度指导信息，实现增强的特征表示。
        通过傅里叶变换将图像转换到频域，在频域中进行特征融合，
        然后转换回空间域。

        处理流程:
        1. 将输入图像转换到频域
        2. 提取幅度信息
        3. 使用亮度信息增强幅度
        4. 重构增强后的特征

        参数:
            x: 输入可见光图像 [B, 3, H, W]
            luminance_map: 处理后的亮度图 [B, 1, H, W]

        返回:
            元组 (幅度增强特征, 填充后的输入)
        """
        # 为FFT操作填充输入到2的幂次尺寸（cuFFT半精度要求）
        luminance_padded, (orig_H, orig_W) = self._pad_to_power_of_two(luminance_map)

        # 从亮度图中提取幅度信息
        luminance_fft = torch.fft.rfft2(luminance_padded, norm="backward")
        luminance_amplitude = torch.abs(luminance_fft)
        # 裁剪幅度特征回原始尺寸再进行卷积处理
        luminance_amplitude = luminance_amplitude[
            :, :, :orig_H, : orig_W // 2 + 1
        ]  # rfft2的输出宽度是(W//2+1)
        luminance_features = self.conv_first_map(luminance_amplitude)

        # 傅里叶域增强（仅使用亮度特征）
        x_enhanced, processed_luminance = self.fourier_processor(
            x, luminance_features
        )

        # 填充输入以确保维度能被8整除
        x_enhanced = self._pad_to_multiple(x_enhanced, multiple=8)
        x_padded = self._pad_to_multiple(x, multiple=8)

        return x_enhanced, x_padded

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        LLIE模型的前向传播

        执行完整的三级低光图像增强流程：
        1. 从输入图像提取亮度特征
        2. 亮度图处理阶段
        3. 傅里叶域增强阶段
        4. 空间-频率双重处理阶段
        5. 输出增强图像

        参数:
            x: 低光输入图像 [B, 3, H, W]

        返回:
            增强后的图像 [B, 3, H, W]
        """
        # 保存原始输入作为可见光图像
        visible = x

        # 提取亮度通道（YUV颜色空间中的Y通道）
        # 使用标准RGB到Y的转换公式：Y = 0.299*R + 0.587*G + 0.114*B
        luminance = 0.299 * x[:, 0:1] + 0.587 * x[:, 1:2] + 0.114 * x[:, 2:3]
        _, _, H, W = visible.shape

        # 第一阶段：亮度图处理 - 为照明提供指导信息
        # 亮度处理器分析图像的照明条件，生成照明指导图
        luminance_map = self.luminance_processor(luminance)

        # 第二阶段：傅里叶域增强 - 亮度特征整合
        # 在频域中融合可见光和亮度信息
        fourier_enhanced, visible_padded = self._get_amplitude_enhancement(
            visible, luminance_map
        )

        # 第三阶段：空间-频率双重处理
        # 结合空间域和频域特征，进行最终的图像增强
        final_enhanced = self.spatial_processor(visible_padded, fourier_enhanced)

        # 裁剪输出以匹配原始输入尺寸
        # 移除填充部分，确保输出尺寸与输入一致
        final_enhanced = final_enhanced[:, :, :H, :W]

        # 返回最终增强的图像
        return final_enhanced

    def _initialize_weights(self):
        """
        使用现代最佳实践初始化模型权重

        权重初始化策略：
        - 卷积层：使用Kaiming正态分布初始化，适配LeakyReLU激活函数
        - 偏置：初始化为0
        - 归一化层：权重初始化为1，偏置初始化为0
        """
        for module in self.modules():
            if isinstance(module, nn.Conv2d):
                # 使用Kaiming初始化，适合LeakyReLU激活函数
                nn.init.kaiming_normal_(module.weight, mode="fan_out", nonlinearity="leaky_relu")
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
            elif isinstance(module, (nn.BatchNorm2d, nn.InstanceNorm2d)):
                # 归一化层的标准初始化
                nn.init.constant_(module.weight, 1)
                nn.init.constant_(module.bias, 0)

    def get_config(self) -> Dict[str, Any]:
        """
        获取模型配置用于序列化

        返回:
            包含模型关键参数的配置字典
        """
        return {
            "type": "LLIE",
            "y_channels": self.y_channels,  # 亮度处理通道数
            "f_channels": self.f_channels,  # 傅里叶处理通道数
            "s_channels": self.s_channels,  # 空间处理通道数
            "input_channels": self.input_channels,  # 输入通道数
        }

    @property
    def num_parameters(self) -> int:
        """获取总参数数量。"""
        return sum(p.numel() for p in self.parameters())

    @property
    def num_trainable_parameters(self) -> int:
        """获取可训练参数数量。"""
        return sum(p.numel() for p in self.parameters() if p.requires_grad)

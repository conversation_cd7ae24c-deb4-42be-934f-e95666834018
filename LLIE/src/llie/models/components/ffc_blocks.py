"""
FFC (Fast Fourier Convolution) blocks for LLIE model.

This module contains the FFC-based processing components used in the second stage
of the LLIE architecture for spatial and frequency domain dual-branch processing.
"""

from typing import Tuple, Optional
import functools
import torch
import torch.nn as nn
import torch.nn.functional as F
from loguru import logger

from ...utils import MODELS, register_model
from .common import ResidualBlock_noBN, ChannelAttentionFusion, make_layer
from .fourier_blocks import MultiConvBlock


class SpectralTransform(nn.Module):
    """
    Spectral Transform layer for frequency domain processing.

    This layer applies convolutions in the frequency domain using FFT,
    enabling efficient global context modeling.

    Args:
        in_channels: Number of input channels.
        out_channels: Number of output channels.
        stride: Convolution stride (default: 1).
        groups: Number of groups for grouped convolution (default: 1).
    """

    def __init__(self, in_channels: int, out_channels: int, stride: int = 1, groups: int = 1):
        super().__init__()

        self.in_channels = in_channels
        self.out_channels = out_channels
        self.stride = stride
        self.groups = groups

        # Convolution for frequency domain processing
        self.conv = nn.Conv2d(in_channels * 2, out_channels * 2, 1, 1, 0, bias=False, groups=groups)
        self.bn = nn.BatchNorm2d(out_channels * 2)
        self.relu = nn.ReLU(inplace=True)

        # Output convolution
        self.conv_out = nn.Conv2d(out_channels, out_channels, 1, 1, 0, groups=groups)

        self._initialize_weights()

    def _initialize_weights(self):
        """Initialize weights using modern best practices."""
        for module in self.modules():
            if isinstance(module, nn.Conv2d):
                nn.init.kaiming_normal_(module.weight, mode="fan_out", nonlinearity="relu")
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
            elif isinstance(module, nn.BatchNorm2d):
                nn.init.constant_(module.weight, 1)
                nn.init.constant_(module.bias, 0)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through spectral transform.

        Args:
            x: Input tensor [B, C, H, W].

        Returns:
            Transformed tensor [B, C, H, W].
        """
        B, C, H, W = x.shape

        # Apply FFT (using rfftn for compatibility)
        fft_dim = (-2, -1)
        x_fft = torch.fft.rfftn(x, dim=fft_dim, norm="ortho")

        # Convert to real/imaginary representation
        x_fft = torch.stack((x_fft.real, x_fft.imag), dim=-1)
        x_fft = x_fft.permute(0, 1, 4, 2, 3).contiguous()  # [B, C, 2, H, W//2+1]
        x_fft = x_fft.view(B, C * 2, H, W // 2 + 1)

        # Apply convolution in frequency domain
        x_fft = self.conv(x_fft)
        x_fft = self.bn(x_fft)
        x_fft = self.relu(x_fft)

        # Reshape back to complex representation
        x_fft = x_fft.view(B, self.out_channels, 2, H, W // 2 + 1)
        x_fft = x_fft.permute(0, 1, 3, 4, 2).contiguous()

        # Convert back to complex tensor
        x_fft = torch.complex(x_fft[..., 0], x_fft[..., 1])

        # Apply inverse FFT
        x = torch.fft.irfftn(x_fft, s=(H, W), dim=fft_dim, norm="ortho")

        # Final convolution
        x = self.conv_out(x)

        return x


class FFCBlock(nn.Module):
    """
    FFC (Fast Fourier Convolution) block combining local and global processing.

    This block processes features through dual branches: one for local spatial
    processing and another for global frequency domain processing, then fuses
    the results for comprehensive feature enhancement.

    Args:
        channels: Number of channels for both local and global branches.
        ratio_gin: Ratio of channels for global input (0.0 to 1.0).
        ratio_gout: Ratio of channels for global output (0.0 to 1.0).
        stride: Convolution stride (default: 1).
        padding: Convolution padding (default: 1).
        dilation: Convolution dilation (default: 1).
        groups: Number of groups for grouped convolution (default: 1).
        bias: Whether to use bias in convolutions (default: False).
        enable_lfu: Whether to enable local feature upsampling (default: True).
    """

    def __init__(
        self,
        channels: int,
        ratio_gin: float = 0.5,
        ratio_gout: float = 0.5,
        stride: int = 1,
        padding: int = 1,
        dilation: int = 1,
        groups: int = 1,
        bias: bool = False,
        enable_lfu: bool = True,
    ):
        super().__init__()

        self.channels = channels
        self.ratio_gin = ratio_gin
        self.ratio_gout = ratio_gout
        self.stride = stride
        self.padding = padding
        self.groups = groups
        self.enable_lfu = enable_lfu

        # Calculate channel splits
        self.global_in_channels = int(channels * ratio_gin)
        self.local_in_channels = channels - self.global_in_channels
        self.global_out_channels = int(channels * ratio_gout)
        self.local_out_channels = channels - self.global_out_channels

        # Local branch (spatial convolution)
        if self.local_in_channels > 0 and self.local_out_channels > 0:
            self.local_conv = nn.Conv2d(
                self.local_in_channels,
                self.local_out_channels,
                kernel_size=3,
                stride=stride,
                padding=padding,
                dilation=dilation,
                groups=groups,
                bias=bias,
            )

        # Global branch (spectral transform)
        if self.global_in_channels > 0 and self.global_out_channels > 0:
            self.global_conv = SpectralTransform(
                self.global_in_channels, self.global_out_channels, stride=stride, groups=groups
            )

        # Cross-branch connections (Local to Global)
        if self.local_in_channels > 0 and self.global_out_channels > 0:
            self.l2g_conv = nn.Conv2d(
                self.local_in_channels, self.global_out_channels, kernel_size=1, bias=bias
            )

        # Cross-branch connections (Global to Local)
        if self.global_in_channels > 0 and self.local_out_channels > 0:
            self.g2l_conv = nn.Conv2d(
                self.global_in_channels, self.local_out_channels, kernel_size=1, bias=bias
            )

        self._initialize_weights()

    def _initialize_weights(self):
        """Initialize weights using modern best practices."""
        for module in self.modules():
            if isinstance(module, nn.Conv2d):
                nn.init.kaiming_normal_(module.weight, mode="fan_out", nonlinearity="relu")
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through FFC block.

        Args:
            x: Input tensor [B, C, H, W].

        Returns:
            Processed tensor [B, C, H, W].
        """
        # Split input into local and global branches
        if self.local_in_channels > 0 and self.global_in_channels > 0:
            x_local, x_global = torch.split(
                x, [self.local_in_channels, self.global_in_channels], dim=1
            )
        elif self.local_in_channels > 0:
            x_local = x
            x_global = None
        else:
            x_local = None
            x_global = x

        # Process through branches
        out_local = None
        out_global = None

        # Local branch processing
        if x_local is not None and self.local_out_channels > 0:
            out_local = self.local_conv(x_local)

            # Add global-to-local connection
            if x_global is not None and hasattr(self, "g2l_conv"):
                out_local = out_local + self.g2l_conv(x_global)

        # Global branch processing
        if x_global is not None and self.global_out_channels > 0:
            out_global = self.global_conv(x_global)

            # Add local-to-global connection
            if x_local is not None and hasattr(self, "l2g_conv"):
                out_global = out_global + self.l2g_conv(x_local)

        # Concatenate outputs
        if out_local is not None and out_global is not None:
            return torch.cat([out_local, out_global], dim=1)
        elif out_local is not None:
            return out_local
        else:
            return out_global


class FFCResnetBlock(nn.Module):
    """
    FFC-based residual block with normalization and activation.

    This block combines FFC processing with residual connections,
    normalization, and activation functions for stable training.

    Args:
        channels: Number of input/output channels.
        ratio_gin: Ratio of channels for global input branch.
        ratio_gout: Ratio of channels for global output branch.
        stride: Convolution stride (default: 1).
        dilation: Convolution dilation (default: 1).
        groups: Number of groups for grouped convolution (default: 1).
        bias: Whether to use bias in convolutions (default: False).
        norm_layer: Normalization layer type (default: nn.BatchNorm2d).
        activation_layer: Activation layer type (default: nn.ReLU).
        enable_lfu: Whether to enable local feature upsampling (default: True).
    """

    def __init__(
        self,
        channels: int,
        ratio_gin: float = 0.5,
        ratio_gout: float = 0.5,
        stride: int = 1,
        dilation: int = 1,
        groups: int = 1,
        bias: bool = False,
        norm_layer: nn.Module = nn.BatchNorm2d,
        activation_layer: nn.Module = nn.ReLU,
        enable_lfu: bool = True,
    ):
        super().__init__()

        # First FFC block
        self.ffc1 = FFCBlock(
            channels, ratio_gin, ratio_gout, stride, 1, dilation, groups, bias, enable_lfu
        )
        self.bn1 = norm_layer(channels)
        self.relu1 = activation_layer(inplace=True)

        # Second FFC block
        self.ffc2 = FFCBlock(
            channels, ratio_gin, ratio_gout, 1, 1, dilation, groups, bias, enable_lfu
        )
        self.bn2 = norm_layer(channels)

        # Final activation
        self.relu2 = activation_layer(inplace=True)

        # Downsample if needed
        self.downsample = None
        if stride != 1:
            self.downsample = nn.Sequential(
                nn.Conv2d(channels, channels, 1, stride, bias=False), norm_layer(channels)
            )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through FFC residual block.

        Args:
            x: Input tensor [B, C, H, W].

        Returns:
            Processed tensor [B, C, H, W].
        """
        identity = x

        # First FFC block
        out = self.ffc1(x)
        out = self.bn1(out)
        out = self.relu1(out)

        # Second FFC block
        out = self.ffc2(out)
        out = self.bn2(out)

        # Residual connection
        if self.downsample is not None:
            identity = self.downsample(identity)

        out += identity
        out = self.relu2(out)

        return out


@register_model("SecondProcessModel")
class SecondProcessModel(nn.Module):
    """
    Second processing stage for spatial and texture reconstruction.

    This model performs the second stage of LLIE processing, using
    dual-branch architecture with FFC blocks and multi-convolution blocks
    for comprehensive spatial and frequency domain processing.

    Args:
        channels: Base number of feature channels (default: 64).
        num_blocks: Number of processing blocks (default: 6).
        input_channels: Number of input channels (default: 3).
        ratio_gin: Global input ratio for FFC blocks (default: 0.5).
        ratio_gout: Global output ratio for FFC blocks (default: 0.5).

    Example:
        >>> model = SecondProcessModel(channels=64, num_blocks=6)
        >>> x = torch.randn(4, 3, 256, 256)  # Input image
        >>> x_four = torch.randn(4, 3, 256, 256)  # Fourier enhanced image
        >>> out = model(x, x_four)
    """

    def __init__(
        self,
        channels: int = 64,
        num_blocks: int = 6,
        input_channels: int = 3,
        ratio_gin: float = 0.5,
        ratio_gout: float = 0.5,
    ):
        super().__init__()

        self.channels = channels
        self.num_blocks = num_blocks

        # Downsampling convolutions
        self.conv1 = nn.Conv2d(input_channels * 2, channels, 3, 1, 1, bias=True)
        self.conv2 = nn.Conv2d(channels, channels, 3, 2, 1, bias=True)
        self.conv3 = nn.Conv2d(channels, channels, 3, 2, 1, bias=True)
        self.activation = nn.LeakyReLU(0.1, inplace=True)

        # Dual-branch processing blocks
        self.ffc_blocks = nn.ModuleList(
            [FFCResnetBlock(channels, ratio_gin, ratio_gout) for _ in range(num_blocks)]
        )
        self.multi_blocks = nn.ModuleList([MultiConvBlock(channels) for _ in range(num_blocks)])

        # Feature fusion
        self.fusion_block = ChannelAttentionFusion(channels)

        # Reconstruction trunk
        ResidualBlock_noBN_f = functools.partial(ResidualBlock_noBN, nf=channels)
        self.recon_trunk = make_layer(ResidualBlock_noBN_f, 1)

        # Upsampling convolutions
        self.upconv1 = nn.Conv2d(channels * 2, channels * 4, 3, 1, 1, bias=True)
        self.upconv2 = nn.Conv2d(channels * 2, channels * 4, 3, 1, 1, bias=True)
        self.upconv3 = nn.Conv2d(channels * 2, channels, 3, 1, 1, bias=True)
        self.upconv_last = nn.Conv2d(channels, input_channels, 3, 1, 1, bias=True)
        self.pixel_shuffle = nn.PixelShuffle(2)

        self._initialize_weights()

    def _initialize_weights(self):
        """Initialize weights using modern best practices."""
        for module in self.modules():
            if isinstance(module, nn.Conv2d):
                nn.init.kaiming_normal_(module.weight, mode="fan_out", nonlinearity="leaky_relu")
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)

    def _upsample_and_concat(
        self, x: torch.Tensor, skip: torch.Tensor, upconv: nn.Module
    ) -> torch.Tensor:
        """
        Upsample features and concatenate with skip connection.

        Args:
            x: Features to upsample.
            skip: Skip connection features.
            upconv: Upsampling convolution layer.

        Returns:
            Upsampled and fused features.
        """
        x_concat = torch.cat([x, skip], dim=1)
        x_up = self.activation(self.pixel_shuffle(upconv(x_concat)))
        return x_up

    def forward(self, x: torch.Tensor, x_four: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through second processing model.

        Args:
            x: Input image [B, 3, H, W].
            x_four: Fourier enhanced image [B, 3, H, W].

        Returns:
            Final enhanced image [B, 3, H, W].
        """
        B, C, H, W = x.shape

        # Concatenate inputs and downsample
        x_input = torch.cat([x, x_four], dim=1)
        x1 = self.activation(self.conv1(x_input))
        x2 = self.activation(self.conv2(x1))
        x3 = self.activation(self.conv3(x2))

        # Dual-branch processing
        ffc_features = x3
        multi_features = x3

        for ffc_block, multi_block in zip(self.ffc_blocks, self.multi_blocks):
            ffc_features = ffc_block(ffc_features)
            multi_features = multi_block(multi_features)

        # Fuse features using channel attention
        fused_features = self.fusion_block(ffc_features, multi_features)

        # Reconstruction and upsampling
        out = self.recon_trunk(fused_features)
        out = self._upsample_and_concat(out, x3, self.upconv1)
        out = self._upsample_and_concat(out, x2, self.upconv2)
        out = self.upconv3(torch.cat([out, x1], dim=1))
        out = self.upconv_last(out)

        # Residual connection with input
        out = out + x

        # Ensure output size matches input size
        out = out[:, :, :H, :W]

        return out

    def get_config(self) -> dict:
        """Get model configuration for serialization."""
        return {
            "type": "SecondProcessModel",
            "channels": self.channels,
            "num_blocks": self.num_blocks,
        }

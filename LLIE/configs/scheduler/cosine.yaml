# 余弦退火学习率调度器配置文件
# 余弦退火调度器能够平滑地降低学习率，有助于模型收敛

# ==================== 余弦退火调度器参数 ====================
scheduler:
  _target_: torch.optim.lr_scheduler.CosineAnnealingLR  # PyTorch余弦退火调度器

  # 调度周期
  T_max: ${trainer.max_epochs}    # 半个余弦周期的长度（通常等于总训练轮数）
                                 # 学习率会从初始值降到eta_min，然后重新开始

  # 最小学习率
  eta_min: 1e-7                  # 学习率的最小值
                                 # 推荐值：初始学习率的1/100到1/1000
                                 # 防止学习率降到过小导致训练停滞

# 余弦退火的优势：
# 1. 平滑的学习率变化，避免突然的跳跃
# 2. 在训练后期提供很小的学习率，有助于精细调优
# 3. 周期性重启可以帮助跳出局部最优

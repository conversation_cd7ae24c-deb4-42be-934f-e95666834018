# Adam优化器配置文件
# Adam是深度学习中最常用的自适应学习率优化器

# ==================== Adam优化器参数 ====================
optimizer:
  _target_: torch.optim.Adam      # PyTorch Adam优化器

  # 学习率（最重要的超参数）
  lr: 0.0002                      # 初始学习率
                                 # 推荐范围：1e-4 到 1e-2
                                 # 较小值：更稳定但收敛慢
                                 # 较大值：收敛快但可能不稳定

  # Adam动量参数
  betas: [0.9, 0.999]            # [beta1, beta2]
                                 # beta1: 一阶矩估计的衰减率（梯度）
                                 # beta2: 二阶矩估计的衰减率（梯度平方）

  # 权重衰减（L2正则化）
  weight_decay: 0                 # 权重衰减系数
                                 # 推荐值：0 到 1e-4
                                 # 用于防止过拟合

  # 数值稳定性参数
  eps: 1e-8                      # 防止除零的小常数

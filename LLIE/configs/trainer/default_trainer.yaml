# LLIE模型默认训练器配置
# 本配置文件定义了训练过程中的所有关键参数

# ==================== 基础训练参数 ====================
max_epochs: 1                     # 最大训练轮数（测试用）
batch_size: 1                     # 训练批次大小（根据GPU显存调整）
val_batch_size: 1                 # 验证批次大小（通常设为1以节省显存）

# ==================== 损失函数配置 ====================
loss:
  type: CharbonnierLoss           # 损失函数类型：CharbonnierLoss（对异常值鲁棒）
  reduction: mean                 # 损失聚合方式：mean/sum
  eps: 1e-6                      # 数值稳定性参数

# ==================== 优化器配置 ====================
optimizer:
  type: Adam                      # 优化器类型：Adam（自适应学习率）
  lr: 0.001                      # 初始学习率（关键超参数）
  weight_decay: 0.0001           # 权重衰减（L2正则化）
  betas: [0.9, 0.999]            # Adam的动量参数
  eps: 1e-8                      # 数值稳定性参数

# ==================== 学习率调度器 ====================
scheduler:
  type: CosineAnnealingLR         # 余弦退火调度器（平滑降低学习率）
  T_max: ${trainer.max_epochs}    # 调度周期（等于总训练轮数）
  eta_min: 1e-6                  # 最小学习率

# ==================== 评估指标 ====================
metrics:
  - psnr                          # 峰值信噪比（越高越好，常用于图像质量评估）
  - ssim                          # 结构相似性指数（越高越好，感知质量指标）
  - mae                           # 平均绝对误差（越低越好）

# ==================== 训练优化技术 ====================
use_ema: true                     # 指数移动平均（提高模型稳定性）
ema_decay: 0.9999                # EMA衰减率

gradient_clip_norm: 1.0           # 梯度裁剪（防止梯度爆炸）

use_amp: false                    # 混合精度训练（暂时禁用以避免cuFFT半精度限制）

# ==================== 检查点保存 ====================
save_freq: 10                     # 每N个epoch保存一次检查点

# ==================== 早停策略 ====================
early_stopping:
  monitor: val_psnr               # 监控的指标（验证集PSNR）
  patience: 20                    # 等待轮数（20轮无改善则停止）
  mode: max                       # 监控模式：max（PSNR越高越好）
  min_delta: 0.001               # 最小改善阈值

# ==================== 验证和日志频率 ====================
val_freq: 1                       # 每N个epoch进行一次验证
log_freq: 10                      # 每N个step记录一次训练指标

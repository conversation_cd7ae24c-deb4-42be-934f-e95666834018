# LOL数据集配置文件
# LOL (Low-Light Object Recognition) 数据集是低光图像增强的标准数据集

# ==================== 数据集基本信息 ====================
type: LOLDataset                  # 数据集类型标识符
name: "LOLv2_Real"               # 数据集名称

# ==================== 训练数据配置 ====================
train:
  # 数据路径配置
  low_light_dir: "data/LOLv2/Real_captured/Train/Low"      # 低光图像目录
  normal_light_dir: "data/LOLv2/Real_captured/Train/Normal" # 正常光照图像目录

  # 训练时的数据增强变换
  transforms:
    # 随机水平翻转（增加数据多样性）
    - type: "RandomHorizontalFlip"
      p: 0.5                      # 50%概率进行翻转

    # 随机旋转（轻微旋转增强鲁棒性）
    - type: "RandomRotation"
      degrees: 10                 # 旋转角度范围：±10度
      p: 0.3                      # 30%概率进行旋转

    # 颜色抖动（增强对光照变化的适应性）
    - type: "ColorJitter"
      brightness: 0.1             # 亮度变化范围：±10%
      contrast: 0.1               # 对比度变化范围：±10%
      saturation: 0.1             # 饱和度变化范围：±10%
      hue: 0.05                   # 色调变化范围：±5%
      p: 0.3                      # 30%概率进行颜色抖动

    # 转换为张量
    - type: "ToTensor"

    # 标准化（使用ImageNet预训练模型的统计值）
    - type: "Normalize"
      mean: [0.485, 0.456, 0.406] # RGB通道均值
      std: [0.229, 0.224, 0.225]  # RGB通道标准差

# ==================== 验证数据配置 ====================
eval:
  # 数据路径配置（使用测试集作为验证集）
  low_light_dir: "data/LOLv2/Real_captured/Test/Low"       # 低光图像目录
  normal_light_dir: "data/LOLv2/Real_captured/Test/Normal" # 正常光照图像目录

  # 验证时的变换（不进行数据增强）
  transforms:
    - type: "ToTensor"            # 转换为张量
    - type: "Normalize"           # 标准化（与训练时保持一致）
      mean: [0.485, 0.456, 0.406]
      std: [0.229, 0.224, 0.225]

# ==================== 测试数据配置 ====================
test:
  # 数据路径配置
  low_light_dir: "data/LOLv2/Real_captured/Test/Low"       # 低光图像目录
  normal_light_dir: "data/LOLv2/Real_captured/Test/Normal" # 正常光照图像目录

  # 测试时的变换（不进行数据增强）
  transforms:
    - type: "ToTensor"            # 转换为张量
    - type: "Normalize"           # 标准化（与训练时保持一致）
      mean: [0.485, 0.456, 0.406]
      std: [0.229, 0.224, 0.225]

# ==================== 图像加载设置 ====================
image_extensions: [".png", ".jpg", ".jpeg", ".bmp", ".tiff"]  # 支持的图像格式
load_size: [128, 128]             # 加载尺寸：调整到128x128以加速训练
crop_size: [128, 128]             # 裁剪尺寸：裁剪到128x128

# ==================== 数据验证设置 ====================
check_paired: true                # 验证低光/正常光图像是否正确配对
allow_missing_pairs: false        # 是否允许跳过缺失的图像对

# ==================== 缓存设置（可选） ====================
cache_images: false               # 是否将图像缓存到内存中（加速加载但占用内存）
max_cache_size: 1000             # 最大缓存图像数量

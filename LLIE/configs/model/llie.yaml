# LLIE 模型配置文件
# LLIE: 低光图像增强网络

type: LLIE                      # 模型类型标识符

# ==================== 模型架构参数 ====================
architecture:
  # 亮度图处理通道数（第一阶段）
  y_nf: 16                        # 推荐值：16-32，影响亮度特征提取能力

  # 傅里叶阶段处理通道数（第二阶段）
  f_nf: 16                        # 推荐值：16-32，影响频域特征处理能力

  # 多阶段处理通道数（第三阶段）
  s_nf: 32                        # 推荐值：32-64，影响空间特征融合能力

  # FFC块数量（Fast Fourier Convolution）
  num_blocks: 6                   # 推荐值：4-8，更多块提供更强表达能力但增加计算量

  # 输入通道数（RGB图像）
  input_channels: 3               # 固定为3（RGB）

# ==================== 模型组件配置 ====================
# 可以通过修改这些配置来进行消融实验
components:
  # 亮度图处理模块
  luminance_map:
    type: LuminanceMap            # 亮度图提取和处理模块
    depth: [1, 1, 1, 1]          # 各层深度配置
    base_channel: ${model.architecture.y_nf}  # 基础通道数（引用上面的y_nf）

  # 傅里叶阶段处理模块
  fourier_stage:
    type: FirstProcessModel       # 第一阶段处理模型
    nf: ${model.architecture.f_nf}     # 特征通道数

  # 多阶段空间处理模块
  multi_stage:
    type: SecondProcessModel      # 第二阶段处理模型
    nf: ${model.architecture.s_nf}     # 特征通道数
    num_blocks: ${model.architecture.num_blocks}      # FFC块数量
    input_channels: ${model.architecture.input_channels}  # 输入通道数

# ==================== 预训练模型 ====================
pretrained: null                 # 预训练模型路径（可选）
                                 # 示例："/path/to/pretrained_model.pth"

# ==================== 模型初始化 ====================
init:
  type: normal                    # 初始化方法：normal/xavier/kaiming
  gain: 0.02                     # 初始化增益（标准差）
